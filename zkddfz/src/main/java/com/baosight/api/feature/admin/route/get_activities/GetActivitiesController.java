package com.baosight.api.feature.admin.route.get_activities;

import java.util.List;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baosight.api.constants.ApiPaths;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
public class GetActivitiesController {
  private final GetActivitiesSerivce getActivitiesSerivce;

  @PostMapping(ApiPaths.Admin.GET_ACTIVITIES)
  public List<GetActivitiesResponseBody> handleRequest(@Valid @RequestBody GetActivitiesRequestBody requestBody) {
    return getActivitiesSerivce.getActivities(requestBody);
  }
}
