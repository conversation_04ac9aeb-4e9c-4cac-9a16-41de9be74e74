package com.baosight.api.feature.admin.route.update_user;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baosight.api.constants.ApiPaths;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
public class UpdateUserController {
  private final UpdateUserService userService;

  @PostMapping(ApiPaths.Admin.UPDATE_USER)
  public void handleRequest(@Valid @RequestBody UpdateUserRequestBody requestBody) {
    userService.updateUser(requestBody);
  }
}
