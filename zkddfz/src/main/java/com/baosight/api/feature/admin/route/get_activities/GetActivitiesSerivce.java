package com.baosight.api.feature.admin.route.get_activities;

import java.util.List;

import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class GetActivitiesSerivce {
  private static final int DEFAULT_PAGE_SIZE = 10;

  private final GetActivitiesMapper getActivitiesMapper;

  public List<GetActivitiesResponseBody> getActivities(GetActivitiesRequestBody requestBody) {
    if (requestBody.getPageLimit() == null || requestBody.getPageLimit() <= 0) {
      requestBody.setPageLimit(DEFAULT_PAGE_SIZE);
    }

    return getActivitiesMapper.query(requestBody);
  }
}
