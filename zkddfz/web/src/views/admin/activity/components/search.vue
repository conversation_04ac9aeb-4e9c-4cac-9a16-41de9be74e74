<template>
  <form class="flex gap-6" @submit.prevent="handleSubmit">
    <div class="flex gap-2">
      <a-button html-type="submit">查询</a-button>

      <a-button @click="reset">重置</a-button>
    </div>

    <div class="flex flex-wrap gap-x-6 gap-y-1">
      <div class="flex items-center gap-2 whitespace-nowrap">
        <label for="username">账号</label>
        <a-input id="username" class="max-w-32" v-model:value="username" allow-clear />
      </div>

      <div class="flex items-center gap-2 whitespace-nowrap">
        <label for="displayName">姓名</label>
        <a-input id="displayName" class="max-w-32" v-model:value="displayName" allow-clear />
      </div>

      <div class="flex items-center gap-2 whitespace-nowrap">
        <label for="activity">操作记录</label>
        <a-input id="activity" class="max-w-40" v-model:value="activity" allow-clear />
      </div>

      <div class="flex items-center gap-2 whitespace-nowrap">
        <label for="createdAtAfter">操作开始时间</label>
        <a-date-picker id="createdAtAfter" v-model:value="createdAtAfter" show-time allow-clear />
      </div>

      <div class="flex items-center gap-2 whitespace-nowrap">
        <label for="createdAtBefore">操作结束时间</label>
        <a-date-picker id="createdAtBefore" v-model:value="createdAtBefore" show-time allow-clear />
      </div>
    </div>
  </form>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue';
import type { Dayjs } from 'dayjs';
import { ref } from 'vue';

const emits = defineEmits(['search']);

const username = ref<string>();

const displayName = ref<string>();

const activity = ref<string>();

const createdAtAfter = ref<Dayjs>();

const createdAtBefore = ref<Dayjs>();

const init = () => {
  activity.value = undefined;
  username.value = undefined;
  displayName.value = undefined;
  createdAtAfter.value = undefined;
  createdAtBefore.value = undefined;
};

const handleSubmit = () => {
  emits('search', {
    activity: activity.value || undefined,
    username: username.value || undefined,
    displayName: displayName.value || undefined,
    createdAtAfter: createdAtAfter.value || undefined,
    createdAtBefore: createdAtBefore.value || undefined,
  });
  message.success('查询条件已更新');
};

const reset = () => {
  init();
  handleSubmit();
};
</script>
