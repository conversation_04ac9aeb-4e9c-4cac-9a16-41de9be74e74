<template>
  <div class="background">
    <!-- class="h-12 flex items-center px-2 border-b bg-neutral-50 border-neutral-200 dark:border-neutral-600 dark:bg-neutral-900" -->
    <header v-if="!hiddenHeader" class="flex items-center px-2 header">
      <!-- <router-link to="/">
        <img src="/baosight.png" alt="宝信软件" class="min-w-20 h-5" />
      </router-link> -->

      <div class="flex gap-2 route_menu">
        <!-- <div class="flex gap-2 ml-2"> -->
        <main-menu />
      </div>

      <!-- 主标题内容 -->
      <div class="home_title">
        <div class="home_title_txt">智能调度仿真系统</div>
      </div>
      <div class="flex gap-2 route_menu_right">
        <menu-right />
      </div>
    </header>

    <main>
      <router-view />
    </main>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores/app';
import { useTitle } from '@vueuse/core';
import { storeToRefs } from 'pinia';
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import MainMenu from './menu/menuLeft.vue';
import MenuRight from './menu/menuRight.vue';

const appStore = useAppStore();
const { appname } = storeToRefs(appStore);

const route = useRoute();

const hiddenHeader = computed(() => route.meta.hiddenHeader);

const title = computed(() => {
  if (route.meta.title) {
    return route.meta.title + ' - ' + appname.value;
  }
  return appname.value;
});

useTitle(title);
</script>
<style>
.background {
  height: 100%;
  background-image: url('../assets/img/bg.png');
  background-repeat: no-repeat;
  background-size: 100% auto;
  /* position: relative;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: aliceblue; */
}

.header {
  position: absolute;
  height: 130px;
  width: 100%;
  background-image: url('../assets/img/bg202.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  opacity: 0.98;
  z-index: 100;
}
.home_title {
  display: flex;
  justify-content: center;
  position: relative;
  width: 44%;
  height: 100%;
}

.home_title_txt {
  font-family: youtitle;
  font-size: 55px;
  background-image: linear-gradient(rgb(255, 255, 255), #8cddf6);
  -webkit-background-clip: text;
  font-smooth: always;
  -webkit-font-smoothing: antialiased;
  color: transparent;
}
@font-face {
  font-family: youtitle;
  src: url('../assets/001.ttf');
}
.route_menu {
  margin-left: 160px;
}
.route_menu_right {
  margin-left: 0px;
}
</style>
