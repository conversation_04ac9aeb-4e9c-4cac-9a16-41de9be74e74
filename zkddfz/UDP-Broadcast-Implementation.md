# UDP广播功能实现文档

## 📋 功能概述

在 `RouteWebSocketHandler.java` 中实现了UDP广播功能，当WebSocket接收到车辆状态更新数据时，会自动通过UDP协议将数据广播给外部系统。

## 🔧 实现的功能

### 1. **UDP广播器 (UdpBroadcaster)**
- 自动初始化UDP广播服务
- 支持广播到默认地址和订阅者地址
- 支持车辆数据列表和单个车辆数据广播
- 支持原始数据广播

### 2. **WebSocket集成**
- 在车辆状态更新处理中自动触发UDP广播
- 支持实时数据转发
- 包含完整的车辆类型信息

### 3. **管理接口**
- REST API管理UDP广播订阅者
- 状态查询和配置信息获取
- 使用说明和帮助信息

## 📁 新增/修改的文件

### 新增文件:
1. **`UdpBroadcaster.java`** - UDP广播器核心类
2. **`UdpBroadcastController.java`** - UDP广播管理REST API
3. **`test-udp-broadcast-client.py`** - Python测试客户端

### 修改文件:
1. **`RouteWebSocketHandler.java`** - 集成UDP广播功能
2. **`application-dev.properties`** - 添加UDP配置

## ⚙️ 配置参数

在 `application-dev.properties` 中添加了以下配置:

```properties
# UDP configuration
udp.server.port=9090                    # UDP接收端口
udp.server.buffer.size=1024            # UDP缓冲区大小
udp.broadcast.port=9091                # UDP广播端口
udp.broadcast.address=***************  # UDP广播地址
udp.broadcast.enabled=true             # 启用UDP广播
```

## 🔄 工作流程

1. **WebSocket连接建立** → 初始化UDP广播器
2. **接收车辆状态数据** → 解析车辆数据列表
3. **处理车辆状态更新** → 记录日志并触发UDP广播
4. **UDP广播** → 发送到默认地址和所有订阅者
5. **连接关闭** → 清理UDP广播器资源

## 📡 数据格式

### WebSocket接收格式:
```json
[
  {
    "device_id": "PCC_12345",
    "position": {"x": 100, "y": 0, "z": 200},
    "rotation": {"x": 0, "y": 90, "z": 0},
    "duration": 0,
    "power": 85,
    "consume": 15,
    "type": "PCC"
  }
]
```

### UDP广播格式:
与WebSocket接收格式完全相同，保持数据一致性。

## 🌐 REST API接口

### 1. 获取状态
```
GET /api/udp-broadcast/status
```

### 2. 添加订阅者
```
POST /api/udp-broadcast/subscribers?address=*************&port=9091
```

### 3. 移除订阅者
```
DELETE /api/udp-broadcast/subscribers?address=*************&port=9091
```

### 4. 获取配置
```
GET /api/udp-broadcast/config
```

### 5. 获取帮助
```
GET /api/udp-broadcast/help
```

## 🧪 测试方法

### 1. 启动服务器
```bash
cd zkddfz
mvn spring-boot:run
```

### 2. 运行UDP测试客户端
```bash
python test-udp-broadcast-client.py
```

### 3. 发送车辆数据
通过WebSocket客户端发送车辆状态数据到服务器

### 4. 验证广播
观察UDP测试客户端是否接收到广播的车辆数据

## 📊 日志输出示例

### WebSocket服务器日志:
```
📊 收到来自 session123 的车辆状态信息: 2 辆车
  - 车辆 PCC_12345: 位置(100, 0, 200), 角度(0, 90, 0), 电量: 85%, 消耗: 15, 类型: PCC
  - 车辆 BBC_67890: 位置(150, 0, 300), 角度(0, 45, 0), 电量: 92%, 消耗: 8, 类型: BBC
📡 已通过UDP广播车辆状态数据: 2 辆车
```

### UDP客户端日志:
```
📨 [14:30:25.123] 收到来自 127.0.0.1:9091 的数据 (#1)
🚗 车辆数量: 2
  🚗 车辆 1: PCC_12345 (类型: PCC)
     📍 位置: X=100, Y=0, Z=200
     🔄 角度: X=0°, Y=90°, Z=0°
     🔋 电量: 85%, 消耗: 15
```

## 🔒 安全考虑

1. **网络安全**: UDP广播在局域网内使用，注意防火墙配置
2. **数据验证**: 接收端应验证数据格式和来源
3. **访问控制**: REST API可添加认证机制
4. **资源管理**: 自动清理连接和资源

## 🚀 扩展功能

1. **数据过滤**: 可添加车辆类型或区域过滤
2. **压缩传输**: 对大量数据进行压缩
3. **加密传输**: 添加数据加密功能
4. **多播支持**: 支持UDP多播协议
5. **监控统计**: 添加广播统计和监控功能

## 🔧 故障排除

### 常见问题:

1. **UDP端口被占用**
   - 检查端口9091是否被其他程序占用
   - 修改配置文件中的端口号

2. **防火墙阻止**
   - 确保防火墙允许UDP 9091端口
   - 检查网络策略设置

3. **数据接收不到**
   - 确认WebSocket连接正常
   - 检查UDP广播器是否正确初始化
   - 验证客户端监听地址和端口

4. **性能问题**
   - 监控UDP广播频率
   - 调整缓冲区大小
   - 优化数据序列化

## ✅ 验证清单

- [ ] WebSocket服务器正常启动
- [ ] UDP广播器成功初始化
- [ ] 车辆状态数据正常接收
- [ ] UDP广播功能正常工作
- [ ] 测试客户端能接收数据
- [ ] REST API接口正常响应
- [ ] 日志输出正确完整
- [ ] 资源清理正常执行
