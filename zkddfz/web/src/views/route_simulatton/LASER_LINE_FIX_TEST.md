# 传感器可视化系统：激光线+平行光功能实现

## 功能概述

传感器可视化系统现在同时具备两种功能：

### 1. 激光线可视化（已优化）
- 显示红色激光线，长度为1500米
- 方向由俯仰角（pitch）和水平角（yaw）决定
- 支持拖拽实时更新方向
- 修复了距离不一致的bug

### 2. 平行光照明（新增功能）
- 在传感器位置添加Three.js的DirectionalLight（平行光）
- 光源属性：红色(0xff0000)，强度2，照射距离1500米
- 产生矩形照射区域，大小由传感器俯仰角和水平角范围决定
- 光源朝向与激光线方向完全一致
- 拖拽传感器时平行光朝向同步更新
- 能够照亮场景中的3D模型，产生真实的矩形光照效果

## 实现内容

### 1. Bug修复
- **问题**：当激光线距离从15米修改为1500米后，拖拽传感器球体时激光线不再实时更新方向
- **原因**：`createLaserLine`函数使用1500米距离，但`updateLaserLine`函数仍使用15米距离，导致距离不一致
- **修复**：统一使用`LASER_CONFIG.DISTANCE`常量，确保创建和更新使用相同的距离参数

### 2. 平行光系统
- 添加`SENSOR_LIGHT_CONFIG`配置常量
- 实现`createSensorLight`函数创建平行光
- 实现`updateSensorLight`函数更新光源朝向
- 根据传感器角度范围设置矩形照射区域大小

## 修改的文件

### SceneManager.vue
1. **添加配置常量**（第67-82行）
```javascript
const LASER_CONFIG = {
  DISTANCE: 1500, // 激光线距离（米）
  OPACITY: 0.8,   // 透明度（提高可见性）
  LINE_WIDTH: 3   // 线宽（提高可见性）
};

const SENSOR_LIGHT_CONFIG = {
  COLOR: 0xff0000,    // 红色光源
  INTENSITY: 2,       // 平行光强度（较低，避免过亮）
  DISTANCE: 1500,     // 光照距离
  SHADOW_MAP_SIZE: 1024, // 阴影贴图尺寸
  SHADOW_CAMERA_SIZE: 200 // 阴影相机尺寸（控制矩形照射区域大小）
};
```

2. **新增createSensorLight函数**（第1506-1576行）
- 创建DirectionalLight（平行光）模拟矩形照射区域
- 根据传感器角度范围设置阴影相机的矩形区域大小
- 配置光源属性：颜色、强度、位置、目标朝向等

3. **修改createSensorSphere函数**（第1414-1422行）
- 在传感器组中添加平行光和目标对象
- 更新userData结构包含光源引用

4. **新增updateSensorLight函数**（第1760-1791行）
- 更新平行光位置和目标位置
- 同步光源朝向与激光线方向

5. **修复updateLaserLine函数**（第1690-1749行）
- 使用`LASER_CONFIG.DISTANCE`替代硬编码的15（修复核心bug）
- 同步调用`updateSensorLight`更新点光源
- 添加错误处理和更新日志

6. **优化清理函数**（第1599-1605行）
- 正确清理平行光和阴影贴图
- 添加光源清理日志

7. **增强测试函数**（第1935-1978行）
- 检查激光线和平行光配置
- 验证光源类型、强度、矩形区域等属性

### index.vue
1. **更新测试函数**（第4559-4599行）
- `testLaserLineDrag`函数用于测试拖拽功能
- 提供激光线和平行光的详细测试说明

2. **新增平行光测试函数**（第4602-4649行）
- `testSensorLighting`函数专门测试平行光照明效果
- 统计场景中的光源数量和类型
- 检查光源属性、矩形区域和可见性

## 测试步骤

### 1. 基础功能测试
1. 打开浏览器开发者工具控制台
2. 运行测试函数：
```javascript
// 测试激光线和平行光拖拽功能
window.testLaserLineDrag();

// 测试平行光照明效果
window.testSensorLighting();

// 或者直接调用SceneManager的测试函数
testLaserLineFunctionality();
```

### 2. 拖拽功能测试
1. 在左侧面板添加一个设备（如斗轮挖掘机）
2. 为设备配置传感器：
   - 传感器名称：测试激光雷达
   - 相对位置：x=0, y=2, z=0
   - 俯仰角范围：-45° 到 45°
   - 水平角范围：-180° 到 180°
   - 启用传感器
3. 在右侧面板开启传感器可视化
4. 在3D场景中找到红色传感器球体
5. 拖拽球体测试激光线是否实时更新方向

### 3. 平行光照明测试
1. 运行平行光测试函数：`window.testSensorLighting()`
2. 观察控制台输出的光源统计信息
3. 在3D场景中观察模型是否被红色平行光照亮
4. 检查平行光是否产生矩形照射区域和阴影效果

### 4. 验证要点
- ✅ 激光线长度应为1500米
- ✅ 拖拽时激光线方向实时更新
- ✅ 平行光朝向与激光线方向一致
- ✅ 平行光产生矩形照射区域，照亮场景中的3D模型
- ✅ 矩形照射区域大小由传感器俯仰角和水平角范围决定
- ✅ 控制台显示角度变化和光源更新日志
- ✅ 激光线在远距离下清晰可见
- ✅ 平行光产生真实的照明和阴影效果

## 预期结果

### 修复前
- 拖拽传感器球体时激光线方向不更新
- 激光线长度不一致（创建时1500米，更新时15米）
- 传感器只有可视化功能，没有实际光照效果

### 修复后
- ✅ 拖拽传感器球体时激光线实时更新方向
- ✅ 激光线长度统一为1500米
- ✅ 激光线在远距离下仍然清晰可见
- ✅ 新增平行光照明功能，产生真实的矩形光照效果
- ✅ 平行光朝向与激光线方向完全同步
- ✅ 矩形照射区域大小根据传感器角度范围自动调整
- ✅ 控制台输出详细的调试信息

## 性能考虑

1. **远距离渲染**：1500米的激光线不会显著影响性能，因为使用的是简单的Line几何体
2. **拖拽响应**：优化了拖拽事件处理，确保流畅的实时更新
3. **内存管理**：正确释放旧的几何体，避免内存泄漏

## 故障排除

### 如果激光线不显示
1. 检查传感器是否启用
2. 检查传感器可视化是否开启
3. 检查相机位置是否能看到激光线

### 如果拖拽不工作
1. 确保不在自由视角模式
2. 检查传感器球体是否可见
3. 检查控制台是否有错误信息

### 如果激光线长度不正确
1. 运行测试函数检查配置
2. 检查`LASER_CONFIG.DISTANCE`值
3. 查看控制台日志确认距离计算
