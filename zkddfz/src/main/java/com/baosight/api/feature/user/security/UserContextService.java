package com.baosight.api.feature.user.security;

import org.springframework.stereotype.Service;

import com.baosight.api.db.user.UserEntity;

import jakarta.servlet.http.HttpServletRequest;

@Service
public class UserContextService {
  private static final String USER_ATTRIBUTE_NAME = "user";

  public UserEntity getUserFromRequest(HttpServletRequest request) {
    return (UserEntity) request.getAttribute(USER_ATTRIBUTE_NAME);
  }

  public void setUserAttribute(HttpServletRequest request, UserEntity user) {
    request.setAttribute(USER_ATTRIBUTE_NAME, user);
  }
}
