package com.baosight.api.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 环境检测工具类
 * 用于检测当前 Spring 应用运行的环境配置
 */
@Component
public class SpringProfileChecker {
  @Value("${spring.profiles.active}")
  private String profile;

  private static final String DEV_PROFILE_NAME = "dev";
  private static final String TEST_PROFILE_NAME = "test";
  private static final String PROD_PROFILE_NAME = "prod";

  /**
   * 检查当前是否为开发环境
   * 
   * @return true 表示开发环境，false 表示非开发环境
   */
  public boolean isDevelopmentProfile() {
    return DEV_PROFILE_NAME.equals(profile);
  }

  /**
   * 检查当前是否为测试环境
   * 
   * @return true 表示测试环境，false 表示非测试环境
   */
  public boolean isTestingProfile() {
    return TEST_PROFILE_NAME.equals(profile);
  }

  /**
   * 检查当前是否为生产环境
   * 
   * @return true 表示生产环境，false 表示非生产环境
   */
  public boolean isProductionProfile() {
    return PROD_PROFILE_NAME.equals(profile);
  }

  /**
   * 获取当前激活的环境配置
   * 
   * @return 当前激活的 profile 名称
   */
  public String getCurrentProfile() {
    return profile;
  }

  /**
   * 检查是否为指定的环境
   * 
   * @param profileName 环境名称
   * @return true表 示匹配，false 表示不匹配
   */
  public boolean isProfile(String profileName) {
    return profileName != null && profileName.equals(profile);
  }
}
