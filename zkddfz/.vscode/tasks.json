{"version": "2.0.0", "tasks": [{"label": "📦 Spring Boot Build", "type": "shell", "command": "${config:maven.home}/bin/mvn", "args": ["clean", "package", "-Dmaven.repo.local=${workspaceFolder}/runtime/.m2/repository"], "windows": {"command": "${config:maven.home}/bin/mvn.cmd", "args": ["clean", "package", "'-Dmaven.repo.local=${workspaceFolder}/runtime/.m2/repository'"]}, "options": {"env": {"JAVA_HOME": "${config:java.jdt.ls.java.home}"}}, "group": {"kind": "build", "isDefault": true}, "problemMatcher": []}, {"label": "⚡ Web Dev", "type": "shell", "command": "${config:node.home}/bin/node", "args": ["node_modules/vite/bin/vite.js"], "windows": {"command": "${config:node.home}/node.exe"}, "options": {"cwd": "${workspaceFolder}/web"}, "dependsOn": "⚙️ Web Pre", "problemMatcher": [], "isBackground": true}, {"label": "📦 Web Build", "type": "shell", "command": "${config:node.home}/bin/node", "args": ["node_modules/vite/bin/vite.js", "build"], "windows": {"command": "${config:node.home}/node.exe"}, "options": {"cwd": "${workspaceFolder}/web"}, "dependsOn": "⚙️ Web Pre", "problemMatcher": []}, {"label": "⚙️ Web Pre", "type": "shell", "command": "${config:node.home}/bin/node", "args": ["scripts/ag-grid"], "windows": {"command": "${config:node.home}/node.exe"}, "options": {"cwd": "${workspaceFolder}/web"}, "problemMatcher": []}, {"label": "📥 Web Install", "type": "shell", "command": "${config:node.home}/bin/pnpm", "args": ["install"], "windows": {"command": "${config:node.home}/pnpm.exe"}, "options": {"cwd": "${workspaceFolder}/web"}, "problemMatcher": []}]}