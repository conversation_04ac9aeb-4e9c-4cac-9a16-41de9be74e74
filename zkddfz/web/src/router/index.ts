import Layout from '@/layout/index.vue';
import AdminActivity from '@/views/admin/activity/index.vue';
import Admin from '@/views/admin/index.vue';
import AdminUser from '@/views/admin/user/index.vue';
import CoordinateSystem from '@/views/coordinate_system/index.vue';
// import Home from '@/views/home/<USER>';
import Login from '@/views/login/index.vue';
import RouteSimulatton from '@/views/route_simulatton/index.vue';
// import RouteSimulattons from '@/views/route_simulattons/index.vue'; // 文件不存在，已注释
import Error404 from '@/views/sys/error-404.vue';
import { createRouter, createWebHistory } from 'vue-router';

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: Layout,
      children: [
        // 默认路由 - 路线模拟控制
        { path: '', name: 'route_simulatton', component: RouteSimulatton, meta: { title: '路线模拟控制' } },
        {
          path: 'coordinate_system',
          name: 'coordinate_system',
          component: CoordinateSystem,
          meta: { title: '坐标系统管理' },
        },
        // 保留首页路由（如果需要的话）
        // { path: 'home', name: 'home', component: Home, meta: { title: '首页' } },
        // {
        //   path: 'route_simulattons',
        //   name: 'route_simulattons',
        //   component: RouteSimulattons,
        //   meta: { title: '路线模拟控制s' },
        // },

        // 账号登录
        { path: 'login', name: 'login', component: Login, meta: { title: '账号登录', hiddenHeader: true } },

        // 管理员
        {
          path: 'admin',
          name: 'admin',
          component: Admin,
          redirect: { name: 'admin/user' },
          children: [
            { path: '', name: 'admin/user', component: AdminUser, meta: { title: '用户管理' } },
            { path: 'activity', name: 'admin/activity', component: AdminActivity, meta: { title: '操作记录' } },
          ],
        },
      ],
    },

    // 如果没有匹配到上面的路由，显示 404 页面
    {
      path: '/:catchAll(.*)*',
      component: Error404,
    },
  ],
});

export default router;
