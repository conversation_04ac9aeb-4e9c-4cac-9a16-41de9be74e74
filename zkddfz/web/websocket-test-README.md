# WebSocket 测试服务器

## 启动测试服务器

1. 确保已安装 Node.js
2. 安装 ws 依赖：
   ```bash
   npm install ws
   ```
3. 启动测试服务器：
   ```bash
   node websocket-test-server.js
   ```

## 测试功能

### 1. 车辆控制电文接收
- 服务器每5秒发送一次车辆控制电文数组
- 电文格式：
  ```json
  [
    {
      "device_id": "device_22924",
      "position": { "x": 905, "y": 0, "z": -970 },
      "rotation": { "x": 0, "y": 90, "z": 0 },
      "duration": 0,
      "power": 85,
      "consume": 15
    }
  ]
  ```

### 2. 车辆状态信息发送
- 前端每秒发送一次所有车辆的状态信息
- 状态格式与控制电文相同

### 3. 测试步骤
1. 启动WebSocket测试服务器
2. 在前端路线模拟控制面板中：
   - 选择"WebSocket实时数据源"
   - 连接地址：`ws://localhost:8080`
   - 点击"连接WebSocket服务器"
3. 观察控制台日志，确认：
   - WebSocket连接成功
   - 开始定时发送车辆状态
   - 接收到车辆控制电文
   - 车辆模型位置和角度更新

### 4. 预期效果
- 场景中的车辆会根据接收到的电文自动移动
- 车辆的电量和消耗值会实时更新
- 前端会定时发送所有车辆的当前状态给后端

## 注意事项
- 确保前端已添加车辆模型到场景中
- 车辆的device_id需要与电文中的device_id匹配
- WebSocket连接成功后会自动开始状态发送
