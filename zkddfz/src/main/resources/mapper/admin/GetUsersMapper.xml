<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.baosight.api.feature.admin.route.get_users.GetUsersMapper">
    
    <select id="query" 
            parameterType="com.baosight.api.feature.admin.route.get_users.GetUsersRequestBody"
            resultType="com.baosight.api.db.user.UserEntity">
        SELECT *
        FROM bx_user
        <where>
            <if test="createdAtAfter != null">
                created_at &gt;= #{createdAtAfter}
            </if>
            <if test="createdAtBefore != null">
                AND created_at &lt;= #{createdAtBefore}
            </if>
            <if test="username != null">
                AND LOWER(username) = LOWER(#{username})
            </if>
            <if test="displayName != null">
                AND LOWER(display_name) = LOWER(#{displayName})
            </if>
        </where>
        ORDER BY id DESC
    </select>
    
</mapper>
