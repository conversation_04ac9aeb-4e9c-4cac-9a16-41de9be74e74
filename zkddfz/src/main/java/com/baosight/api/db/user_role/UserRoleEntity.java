package com.baosight.api.db.user_role;

import java.time.Instant;

import org.hibernate.annotations.Comment;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import com.baosight.api.db.role.RoleNameEnums;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Data
@Entity
@Table(name = "bx_user_role")
@Comment("用户-角色关联表")
public class UserRoleEntity {
  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Comment("ID")
  private Long id;

  @Column(name = "username", nullable = false)
  @Comment("用户名")
  private String username;

  @Enumerated(EnumType.STRING)
  @Column(name = "role_name", nullable = false)
  @Comment("角色名")
  private RoleNameEnums roleName;

  @CreationTimestamp
  @Column(name = "created_at", nullable = false, updatable = false)
  @Comment("创建时间")
  private Instant createdAt;

  @UpdateTimestamp
  @Column(name = "updated_at", nullable = false)
  @Comment("更新时间")
  private Instant updatedAt;
}
