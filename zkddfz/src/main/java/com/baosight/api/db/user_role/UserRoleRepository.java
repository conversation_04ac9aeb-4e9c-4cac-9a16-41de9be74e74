package com.baosight.api.db.user_role;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;

import com.baosight.api.db.role.RoleNameEnums;

public interface UserRoleRepository extends JpaRepository<UserRoleEntity, Long> {
  void deleteByRoleName(RoleNameEnums roleName);

  void deleteByUsername(String username);

  boolean existsByUsernameAndRoleName(String username, RoleNameEnums roleName);

  Optional<UserRoleEntity> findByUsernameAndRoleName(String username, RoleNameEnums roleName);

  List<UserRoleEntity> findAllByRoleName(RoleNameEnums roleName);

  List<UserRoleEntity> findAllByUsername(String username);

  List<UserRoleEntity> findByUsernameIn(List<String> usernames);
}
