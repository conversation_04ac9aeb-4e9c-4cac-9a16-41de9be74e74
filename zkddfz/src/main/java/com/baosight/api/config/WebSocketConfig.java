package com.baosight.api.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import com.baosight.api.websocket.RouteWebSocketHandler;

/**
 * WebSocket 配置类
 * 用于配置路线仿真的 WebSocket 服务
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Bean
    public RouteWebSocketHandler routeWebSocketHandler() {
        return new RouteWebSocketHandler();
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // 注册路线 WebSocket 处理器，允许跨域访问
        registry.addHandler(routeWebSocketHandler(), "/route")
                .setAllowedOrigins("*"); // 在生产环境中应该限制具体的域名
    }
}
