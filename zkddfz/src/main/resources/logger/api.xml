<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <springProperty scope="context" name="charset" source="logging.charset.console" />

  <appender name="api" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>logs/api.log</file>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <fileNamePattern>logs/api.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
      <maxFileSize>100MB</maxFileSize>
      <maxHistory>30</maxHistory>
      <totalSizeCap>10GB</totalSizeCap>
      <cleanHistoryOnStart>true</cleanHistoryOnStart>
    </rollingPolicy>
    <encoder class="net.logstash.logback.encoder.LogstashEncoder">
      <includeContext>false</includeContext>
      <includeCallerData>true</includeCallerData>
      <fieldNames>
        <timestamp>timestamp</timestamp>
        <message>message</message>
        <thread>[ignore]</thread>
        <logger>[ignore]</logger>
        <version>[ignore]</version>
        <levelValue>[ignore]</levelValue>
        <caller>trace</caller>
        <stackTrace>exception</stackTrace>
        <mdc>context</mdc>
      </fieldNames>
    </encoder>
  </appender>

  <appender name="api_error" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>logs/api.error.log</file>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <fileNamePattern>logs/api.error.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
      <maxFileSize>100MB</maxFileSize>
      <maxHistory>30</maxHistory>
      <totalSizeCap>10GB</totalSizeCap>
      <cleanHistoryOnStart>true</cleanHistoryOnStart>
    </rollingPolicy>
    <encoder class="net.logstash.logback.encoder.LogstashEncoder">
      <includeContext>false</includeContext>
      <includeCallerData>true</includeCallerData>
      <fieldNames>
        <timestamp>timestamp</timestamp>
        <message>message</message>
        <thread>[ignore]</thread>
        <logger>[ignore]</logger>
        <version>[ignore]</version>
        <levelValue>[ignore]</levelValue>
        <caller>trace</caller>
        <stackTrace>exception</stackTrace>
        <mdc>context</mdc>
      </fieldNames>
    </encoder>
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>ERROR</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
  </appender>

  <appender name="api_console_highlighted" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>%cyan(%d{HH:mm:ss}) %highlight(%-5level) %highlight(%msg%throwable) > %caller{1}</pattern>
      <charset>UTF-8</charset>
    </encoder>
  </appender>

  <springProfile name="dev">
    <logger name="com.baosight.api" level="DEBUG" additivity="false">
      <appender-ref ref="api" />
      <appender-ref ref="api_error" />
      <appender-ref ref="api_console_highlighted" />
    </logger>

    <logger name="org.springframework" level="INFO" additivity="false">
      <appender-ref ref="api_console_highlighted" />
    </logger>

    <logger name="org.hibernate" level="INFO" additivity="false">
      <appender-ref ref="api_console_highlighted" />
    </logger>

    <root level="INFO">
      <appender-ref ref="api_console_highlighted" />
      <appender-ref ref="api" />
    </root>
  </springProfile>

  <springProfile name="!dev">
    <logger name="com.baosight.api" level="INFO" additivity="false">
      <appender-ref ref="api" />
      <appender-ref ref="api_error" />
      <appender-ref ref="api_console_highlighted" />
    </logger>

    <logger name="org.springframework" level="WARN" additivity="false">
      <appender-ref ref="api_console_highlighted" />
    </logger>

    <root level="WARN">
      <appender-ref ref="api_console_highlighted" />
      <appender-ref ref="api" />
      <appender-ref ref="api_error" />
    </root>
  </springProfile>
</configuration>
