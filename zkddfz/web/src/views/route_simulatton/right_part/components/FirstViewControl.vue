<script setup lang="ts">
import { ref } from 'vue';

const targetPosition = ref({ x: 0, y: 0, z: 0 });
const enableKeyboardControl = ref(false);
const enableKeyboardSlide = ref(false);
const stepLength = ref(1);
</script>

<template>
  <div
    class="futuristic-card-symmetric relative overflow-hidden group bg-gradient-to-br from-slate-800/80 to-slate-700/60 border border-cyan-500/30 shadow-lg shadow-cyan-500/10 hover:shadow-cyan-500/20 transition-all duration-300 max-w-xs w-full"
  >
    <!-- Card Glow Effect -->
    <div
      class="absolute inset-0 bg-gradient-to-br from-cyan-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-md"
    ></div>

    <!-- Corner Decorations -->
    <div class="absolute top-2 left-2 w-4 h-4 border-l-2 border-t-2 border-cyan-400/60"></div>
    <div class="absolute top-2 right-2 w-4 h-4 border-r-2 border-t-2 border-cyan-400/60"></div>
    <div class="absolute bottom-2 left-2 w-4 h-4 border-l-2 border-b-2 border-cyan-400/60"></div>
    <div class="absolute bottom-2 right-2 w-4 h-4 border-r-2 border-b-2 border-cyan-400/60"></div>

    <!-- Card Content -->
    <div class="relative z-10 p-6">
      <!-- Header -->
      <div class="mb-4">
        <h2 class="text-base font-semibold text-cyan-400 mb-2">第一视角控制</h2>
        <div class="w-12 h-0.5 bg-gradient-to-r from-cyan-400 to-transparent rounded-full"></div>
      </div>

      <!-- Camera Offset Settings -->
      <div class="mb-6">
        <h3 class="text-sm font-semibold mb-3 text-white">相机偏移设置:</h3>
        <div class="grid grid-cols-3 gap-2">
          <div>
            <label for="pos-x" class="block text-xs font-medium text-slate-400 mb-1">X (左右):</label>
            <input
              id="pos-x"
              type="number"
              v-model.number="targetPosition.x"
              class="futuristic-input-symmetric w-full px-3 py-2 bg-slate-700/60 border border-cyan-500/30 text-white text-sm placeholder-slate-400 focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200 hover:border-cyan-500/50"
            />
          </div>
          <div>
            <label for="pos-y" class="block text-xs font-medium text-slate-400 mb-1">Y (高度):</label>
            <input
              id="pos-y"
              type="number"
              v-model.number="targetPosition.y"
              class="futuristic-input-symmetric w-full px-3 py-2 bg-slate-700/60 border border-cyan-500/30 text-white text-sm placeholder-slate-400 focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200 hover:border-cyan-500/50"
            />
          </div>
          <div>
            <label for="pos-z" class="block text-xs font-medium text-slate-400 mb-1">Z (前后):</label>
            <input
              id="pos-z"
              type="number"
              v-model.number="targetPosition.z"
              class="futuristic-input-symmetric w-full px-3 py-2 bg-slate-700/60 border border-cyan-500/30 text-white text-sm placeholder-slate-400 focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200 hover:border-cyan-500/50"
            />
          </div>
        </div>
      </div>

      <!-- Keyboard Control -->
      <div class="mb-4">
        <h3 class="text-sm font-semibold mb-3 text-white">键盘控制:</h3>
        <div class="space-y-3">
          <!-- Follow Model Rotation -->
          <div
            class="control-btn-symmetric relative overflow-hidden group bg-gradient-to-r from-slate-700/60 to-slate-600/40 border border-cyan-500/30 p-3 transition-all duration-300 hover:from-slate-600/70 hover:to-slate-500/50 hover:border-cyan-400/50 cursor-pointer"
          >
            <div class="flex items-center space-x-3 relative z-10">
              <div class="relative">
                <input type="checkbox" id="enable-keyboard-control" v-model="enableKeyboardControl" class="sr-only" />
                <div
                  class="w-5 h-5 border-2 transition-all duration-200 flex items-center justify-center"
                  :class="
                    enableKeyboardControl
                      ? 'bg-cyan-500/20 border-cyan-400 shadow-sm shadow-cyan-400/50'
                      : 'border-cyan-500/50 bg-slate-800/60 hover:border-cyan-400/70'
                  "
                  @click="enableKeyboardControl = !enableKeyboardControl"
                >
                  <div
                    v-if="enableKeyboardControl"
                    class="w-2 h-2 bg-cyan-400 transition-all duration-200 shadow-sm shadow-cyan-400/50"
                  ></div>
                </div>
              </div>
              <label
                for="enable-keyboard-control"
                class="text-sm font-medium text-slate-200 cursor-pointer flex-1 select-none"
              >
                跟随模型旋转
              </label>
            </div>
            <!-- Button Glow -->
            <div
              class="absolute inset-0 bg-gradient-to-r from-cyan-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm pointer-events-none"
            ></div>
          </div>

          <!-- Smooth Follow -->
          <div
            class="control-btn-symmetric relative overflow-hidden group bg-gradient-to-r from-slate-700/60 to-slate-600/40 border border-cyan-500/30 p-3 transition-all duration-300 hover:from-slate-600/70 hover:to-slate-500/50 hover:border-cyan-400/50 cursor-pointer"
          >
            <div class="flex items-center space-x-3 relative z-10">
              <div class="relative">
                <input type="checkbox" id="enable-keyboard-slide" v-model="enableKeyboardSlide" class="sr-only" />
                <div
                  class="w-5 h-5 border-2 transition-all duration-200 flex items-center justify-center"
                  :class="
                    enableKeyboardSlide
                      ? 'bg-cyan-500/20 border-cyan-400 shadow-sm shadow-cyan-400/50'
                      : 'border-cyan-500/50 bg-slate-800/60 hover:border-cyan-400/70'
                  "
                  @click="enableKeyboardSlide = !enableKeyboardSlide"
                >
                  <div
                    v-if="enableKeyboardSlide"
                    class="w-2 h-2 bg-cyan-400 transition-all duration-200 shadow-sm shadow-cyan-400/50"
                  ></div>
                </div>
              </div>
              <label
                for="enable-keyboard-slide"
                class="text-sm font-medium text-slate-200 cursor-pointer flex-1 select-none"
              >
                平滑跟随
              </label>
            </div>
            <!-- Button Glow -->
            <div
              class="absolute inset-0 bg-gradient-to-r from-cyan-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm pointer-events-none"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Only keeping essential clip-path styles that can't be done with Tailwind */
.futuristic-card-symmetric {
  clip-path: polygon(1.5rem 0, 100% 0, 100% calc(100% - 1.5rem), calc(100% - 1.5rem) 100%, 0 100%, 0 1.5rem);
}

.futuristic-input-symmetric {
  clip-path: polygon(0.5rem 0, 100% 0, 100% calc(100% - 0.5rem), calc(100% - 0.5rem) 100%, 0 100%, 0 0.5rem);
}

.control-btn-symmetric {
  clip-path: polygon(0.75rem 0, 100% 0, 100% calc(100% - 0.75rem), calc(100% - 0.75rem) 100%, 0 100%, 0 0.75rem);
}
</style>
