package com.baosight.api.db.role;

import org.springframework.stereotype.Service;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class RoleInitializer {
  private final RoleRepository roleRepository;

  private void upsert(RoleNameEnums roleName) {
    RoleEntity roleEntity = roleRepository.findByRoleName(roleName)
        .orElse(new RoleEntity());

    roleEntity.setRoleName(roleName);

    roleRepository.save(roleEntity);
  }

  @Transactional
  public void init() {
    upsert(RoleNameEnums.管理员);
    upsert(RoleNameEnums.用户);
  }
}
