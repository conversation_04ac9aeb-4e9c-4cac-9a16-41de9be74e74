# 🚀 项目开发指南

## 📋 环境要求

### 必装软件

- **VS Code** - 主要开发环境
- **Git** - 版本控制

### 自动配置

项目已包含完整的开发环境配置，无需手动安装 Node.js、Java 等运行时。

---

## ⚡ 快速开始

### 1. 项目准备

```bash
# 建议将项目目录重命名为实际项目名称
# 例如：equip4j → my-awesome-project
```

### 2. 一键初始化

进入项目根目录，根据操作系统选择对应的执行方式：

| 操作系统        | 进入终端                                      | 执行命令          |
| --------------- | --------------------------------------------- | ----------------- |
| **Windows**     | 右键点击项目根目录，选择 "Open Git Bash here" | `bash startup.sh` |
| **Linux/macOS** | 使用系统自带的终端                            | `bash startup.sh` |

### 3. 自动完成项目

脚本将自动完成以下操作：

- ✅ 检测并配置 Node.js 运行环境
- ✅ 安装前端依赖包（使用 pnpm）
- ✅ 安装必要的 VS Code 扩展
- ✅ 生成项目工作区配置
- ✅ 启动 VS Code 开发环境

---

## 🛠️ 日常开发

### 启动开发环境

#### 后端开发（Spring Boot）

```
启动 Spring Boot 开发：运行 → 启动调试 (快捷键: F5)
```

- 🔥 **热重载**：代码修改后自动重启
- 🐛 **断点调试**：完整的调试功能支持
- 📊 **日志输出**：实时查看应用日志

#### 前端开发（Web）

```
启动 Web 开发：终端 → 运行任务 → ⚡ Web Dev
```

- 🌐 **实时预览**：代码修改后自动刷新浏览器
- 🔧 **开发工具**：集成 Vue 开发者工具
- 🌉 **无感跨域**：开发环境自动配置跨域代理

### 常用快捷键

| 快捷键         | 功能              |
| -------------- | ----------------- |
| `F5`           | 启动/重启后端调试 |
| `Ctrl+Shift+P` | 打开命令面板      |
| ` Ctrl+`` `    | 打开终端          |
| `Ctrl+P`       | 快速打开文件      |

---

## 🔧 故障排除

### 常见问题

#### Q1: 初始化脚本执行失败

如果一键脚本遇到问题，也可以分步执行：

```bash
# 1. 安装 VS Code 扩展
## VS Code 进入扩展管理 (Ctrl + Shift + X)
## 扩展搜索框中输入 @recommended，安装全部推荐的扩展

# 2. 生成工作区
## Windows
./runtime/node-v22.16.0/node.exe scripts/generate-workspace.js
## Linux/macOS
./runtime/node-v22.16.0/bin/node scripts/generate-workspace.js

# 3. 安装 Web 依赖（如果有 web 目录）
## Windows
cd web && ../runtime/node-v22.16.0/pnpm.exe install
## Linux/macOS
cd web && ../runtime/node-v22.16.0/bin/pnpm install

# 4. 通过 VS Code 打开项目根目录下的工作区文件
```

#### Q2: VS Code 打开工作区后提示: Please download and install a JDK to compile your project.

- 在手动信任工作区后，需要关闭 VS Code 后重新打开工作区
- 确认下载的 `runtime` 与当前操作系统匹配

#### Q3: 前端依赖安装失败

**排查步骤：**

- 检查网络连接是否正常
- 确认 `runtime` 目录中的 Node.js 版本与当前操作系统匹配
- 尝试重新运行初始化脚本
- 如果问题持续，可手动安装依赖（参考 Q1）

#### Q4: 后端启动失败

**排查步骤：**

- 确认 `runtime` 目录中的 JDK 和 Maven 版本与当前操作系统架构匹配
- 清理 Maven 缓存：删除 `runtime/.m2` 目录，重启 VS Code 后等待 Java 扩展重新下载依赖
- 查看 VS Code "输出" 面板中的详细错误日志进行定位
- 检查后端端口占用：确保端口未被其他应用程序占用

#### Q5: 开发服务无法正常工作

**前端服务问题：**

- 确认前端开发服务器正常启动（默认端口 5173）
- 检查浏览器控制台是否有错误信息

**后端 API 调用问题：**

- 确保后端服务已启动（默认端口 8080）
- Vite 代理会自动将前端的 `/api` 请求转发到后端
- 如有跨域问题，检查 `web/vite.config.js` 中的代理配置

### 💡 获取帮助

如果以上方案仍无法解决问题：

1. **查看完整日志**：保留终端/控制台的完整错误信息
2. **检查系统环境**：确认操作系统版本和架构（x86/x64）
3. **尝试清理重置**：删除 `web/node_modules` 后重新运行初始化脚本
4. **寻求支持**：携带错误信息到官方文档或社区求助

---

## 📚 更多资源

### 官方文档

- 📖 [Equip4J 官方文档](https://ime.baocloud.cn/equip4j) - 完整的框架使用指南
- 🎯 [API 参考](https://ime.baocloud.cn/equip4j/api) - 详细的 API 文档
- 💡 [最佳实践](https://ime.baocloud.cn/equip4j/best-practices) - 开发建议和技巧

### 社区支持

- 💬 [问题反馈](https://github.com/your-org/equip4j/issues) - 提交 Bug 和功能请求
- 🤝 [讨论区](https://github.com/your-org/equip4j/discussions) - 技术讨论和交流

---

## 🎉 开始开发吧！

现在你已经准备好开始开发了！如果遇到任何问题，请参考上述故障排除部分或查阅官方文档。

**Happy Coding!** 🚀✨
