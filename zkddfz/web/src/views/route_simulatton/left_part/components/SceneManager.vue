<script setup lang="ts">
import { ref, watch } from 'vue';

interface Props {
  sceneManagerRef?: any;
  websocketSimulator?: any;
}

const props = defineProps<Props>();
const emit = defineEmits(['scene-settings-changed', 'smooth-animation-changed']);

// 折叠状态
const isExpanded = ref(false);

// 地图材质设置
const mapMaterial = ref({
  metalness: 0.1,
  roughness: 0.8
});

// 环境光设置
const lighting = ref({
  color: '#ffffff',
  intensity: 1.0
});

// 天气设置
const weatherOptions = [
  { id: 'clear', name: '晴天', icon: '☀️' },
  { id: 'rain', name: '下雨', icon: '🌧️' },
  { id: 'snow', name: '下雪', icon: '❄️' }
];

const currentWeather = ref('clear');

// 延时队列动画设置
const smoothAnimation = ref({
  enabled: true,
  delayTime: 2000, // 延时时间（毫秒）
  processInterval: 1000, // 处理间隔（毫秒）
  uniformMotion: true, // 启用匀速运动模式
  moveSpeed: 20 // 移动速度（单位/秒）
});

// 监听材质变化
watch(mapMaterial, (newMaterial) => {
  if (props.sceneManagerRef) {
    try {
      // 调用场景管理器更新地图材质
      if (props.sceneManagerRef.updateMapMaterial) {
        props.sceneManagerRef.updateMapMaterial(newMaterial);
      }
      emit('scene-settings-changed', { type: 'material', data: newMaterial });
    } catch (error) {
      console.error('更新地图材质失败:', error);
    }
  }
}, { deep: true });

// 监听光照变化
watch(lighting, (newLighting) => {
  if (props.sceneManagerRef) {
    try {
      const colorHex = newLighting.color.replace('#', '');
      const colorValue = parseInt(colorHex, 16);
      if (props.sceneManagerRef.updateAmbientLight) {
        props.sceneManagerRef.updateAmbientLight(colorValue, newLighting.intensity);
      }
      emit('scene-settings-changed', { type: 'lighting', data: newLighting });
      console.log('🔆 环境光已更新:', { color: newLighting.color, intensity: newLighting.intensity });
    } catch (error) {
      console.error('更新环境光失败:', error);
    }
  }
}, { deep: true });

// 监听天气变化
watch(currentWeather, (newWeather) => {
  if (props.sceneManagerRef) {
    try {
      if (props.sceneManagerRef.updateWeather) {
        props.sceneManagerRef.updateWeather(newWeather);
      }
      emit('scene-settings-changed', { type: 'weather', data: newWeather });
    } catch (error) {
      console.error('更新天气失败:', error);
    }
  }
});

// 监听流畅动画设置变化
watch(smoothAnimation, (newSettings) => {
  if (props.websocketSimulator) {
    try {
      // 更新WebSocket模拟器的流畅动画设置
      if (props.websocketSimulator.setSmoothAnimation) {
        props.websocketSimulator.setSmoothAnimation(newSettings.enabled);
      }

      // 如果有流畅动画控制器，更新其配置
      if (props.websocketSimulator.smoothAnimationController) {
        const controller = props.websocketSimulator.smoothAnimationController;
        controller.options.delayTime = newSettings.delayTime;
        controller.options.processInterval = newSettings.processInterval;
        controller.options.uniformMotion = newSettings.uniformMotion;

        // 更新所有设备的移动速度
        controller.animationStates.forEach((animationState) => {
          animationState.speed = newSettings.moveSpeed;
        });
      }

      emit('smooth-animation-changed', newSettings);
      console.log('流畅动画设置已更新:', newSettings);
    } catch (error) {
      console.error('更新流畅动画设置失败:', error);
    }
  }
}, { deep: true });

// 重置为默认设置
const resetToDefaults = () => {
  mapMaterial.value = { metalness: 0.1, roughness: 0.8 };
  lighting.value = { color: '#ffffff', intensity: 1.0 };
  currentWeather.value = 'clear';
  smoothAnimation.value = {
    enabled: true,
    delayTime: 2000,
    processInterval: 1000,
    uniformMotion: true,
    moveSpeed: 20
  };
  console.log('场景设置已重置为默认值');
};

// 调试函数
const testEmptyArray = () => {
  if (props.websocketSimulator && props.websocketSimulator.testEmptyArrayHandling) {
    props.websocketSimulator.testEmptyArrayHandling();
  } else {
    console.warn('WebSocket模拟器未初始化或不支持测试功能');
  }
};

const testYCoordinate = () => {
  if (props.websocketSimulator && props.websocketSimulator.testYCoordinateFix) {
    props.websocketSimulator.testYCoordinateFix();
  } else {
    console.warn('WebSocket模拟器未初始化或不支持测试功能');
  }
};

const getAnimationStatus = () => {
  if (props.websocketSimulator && props.websocketSimulator.getSmoothAnimationStatus) {
    const status = props.websocketSimulator.getSmoothAnimationStatus();
    console.log('流畅动画状态:', status);
    alert('动画状态已输出到控制台，请查看F12开发者工具');
  } else {
    console.warn('WebSocket模拟器未初始化或不支持状态查询');
  }
};

const stopAllAnimations = () => {
  if (props.websocketSimulator && props.websocketSimulator.smoothAnimationController) {
    props.websocketSimulator.smoothAnimationController.stopAllAnimations();
    console.log('已停止所有流畅动画');
  } else {
    console.warn('流畅动画控制器未初始化');
  }
};

const testYDisplay = () => {
  if (props.websocketSimulator && props.websocketSimulator.testYCoordinateShaking) {
    props.websocketSimulator.testYCoordinateShaking();
    console.log('Y坐标显示测试已启动');
  } else {
    console.warn('WebSocket模拟器未初始化或不支持测试功能');
  }
};

const testYValues = () => {
  if (props.websocketSimulator && props.websocketSimulator.testDifferentYCoordinates) {
    props.websocketSimulator.testDifferentYCoordinates();
    console.log('🧪 不同Y坐标值测试已启动');
  } else {
    console.warn('⚠️ WebSocket模拟器未初始化或不支持测试功能');
  }
};

const testDelayQueue = () => {
  if (props.websocketSimulator && props.websocketSimulator.testUniformMotion) {
    props.websocketSimulator.testUniformMotion();
    console.log('🧪 延时队列测试已启动');
  } else {
    console.warn('⚠️ WebSocket模拟器未初始化或不支持测试功能');
  }
};

const testSmoothAnimation = () => {
  if (props.websocketSimulator && props.websocketSimulator.testQueueBuffer) {
    props.websocketSimulator.testQueueBuffer();
    console.log('🧪 流畅动画测试已启动');
  } else {
    console.warn('⚠️ WebSocket模拟器未初始化或不支持测试功能');
  }
};

const testDataFlow = () => {
  if (props.websocketSimulator && props.websocketSimulator.testDataFlow) {
    props.websocketSimulator.testDataFlow();
    console.log('🧪 数据流调试已启动');
  } else {
    console.warn('⚠️ WebSocket模拟器未初始化或不支持测试功能');
  }
};
</script>

<template>
  <div
    class="relative overflow-hidden group"
  >
    <div class="relative z-10">
      
      <!-- 内容区域 - 可折叠 -->
      <div 
        class="overflow-hidden transition-all"
      >
        <div class="pt-0 space-y-4">
          <!-- 地图材质控制 -->
          <div>
            <label class="block text-xs text-white/70 mb-2">地图材质</label>
            <div class="flex items-center gap-2">
              <div class="w-100">
                <label class="block text-xs text-slate-400 mb-1">金属度 ({{ mapMaterial.metalness }})</label>
                <input
                  v-model.number="mapMaterial.metalness"
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  class="w-full h-2 bg-slate-700/60 rounded-sm outline-none appearance-none slider"
                />
              </div>
              <div class="w-100">
                <label class="block text-xs text-slate-400 mb-1">粗糙度 ({{ mapMaterial.roughness }})</label>
                <input
                  v-model.number="mapMaterial.roughness"
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  class="w-full h-2 bg-slate-700/60 rounded-sm outline-none appearance-none slider"
                />
              </div>
            </div>
          </div>

          <!-- 环境光设置 -->
          <div>
            <label class="block text-xs text-white/70 mb-1">环境光设置</label>
            <div class="flex items-center gap-2">
              <!-- 光照颜色 -->
              <div class="mb-1">
                <label class="block text-xs text-slate-400 mb-2">光照颜色</label>
                <div class="flex items-center gap-3">
                  <input
                    v-model="lighting.color"
                    type="color"
                    class="w-12 h-8 bg-slate-700/60 border border-purple-500/30 rounded cursor-pointer"
                  />
                  <span class="text-xs text-slate-300 font-mono">{{ lighting.color.toUpperCase() }}</span>
                </div>
              </div>

              <!-- 光照强度 -->
              <div class="w-200">
                <label class="block text-xs text-slate-400 mb-2">光照强度 ({{ lighting.intensity.toFixed(1) }})</label>
                <input
                  v-model.number="lighting.intensity"
                  type="range"
                  min="0"
                  max="30"
                  step="0.1"
                  class="w-full h-2 bg-slate-700/60 rounded-sm outline-none appearance-none slider"
                />
                <div class="flex justify-between text-xs text-slate-500 mt-1">
                  <span>0</span>
                  <span>15</span>
                  <span>30</span>
                </div>
              </div>
            </div>
            
          </div>

          <!-- 天气设置 -->
          <div>
            <label class="block text-xs text-white/70 mb-2">天气</label>
            <div class="grid grid-cols-3 gap-2">
              <button
                v-for="weather in weatherOptions"
                :key="weather.id"
                @click="currentWeather = weather.id"
                :class="[
                  'px-3 py-2 text-xs rounded border transition-all duration-200 flex items-center justify-center gap-1',
                  currentWeather === weather.id
                    ? 'bg-blue-500/30 border-blue-400 text-blue-300'
                    : 'bg-slate-700/60 border-slate-600 text-slate-300 hover:bg-slate-600/60'
                ]"
              >
                <span>{{ weather.icon }}</span>
                <span>{{ weather.name }}</span>
              </button>
            </div>
          </div>

          <!-- 调试工具 -->
          <!-- <div v-if="smoothAnimation.enabled" class="pt-2 border-t border-purple-500/20">
            <label class="block text-xs text-white/70 mb-2">调试工具</label>
            <div class="grid grid-cols-2 gap-2">
              <button
                @click="testEmptyArray"
                class="px-2 py-1 text-xs bg-orange-600/60 border border-orange-500 text-orange-300 rounded hover:bg-orange-600/80 transition-all duration-200"
              >
                测试空数组
              </button>
              <button
                @click="testYCoordinate"
                class="px-2 py-1 text-xs bg-blue-600/60 border border-blue-500 text-blue-300 rounded hover:bg-blue-600/80 transition-all duration-200"
              >
                测试Y坐标
              </button>
              <button
                @click="getAnimationStatus"
                class="px-2 py-1 text-xs bg-green-600/60 border border-green-500 text-green-300 rounded hover:bg-green-600/80 transition-all duration-200"
              >
                查看状态
              </button>
              <button
                @click="stopAllAnimations"
                class="px-2 py-1 text-xs bg-red-600/60 border border-red-500 text-red-300 rounded hover:bg-red-600/80 transition-all duration-200"
              >
                停止动画
              </button>
              <button
                @click="testYDisplay"
                class="px-2 py-1 text-xs bg-green-600/60 border border-green-500 text-green-300 rounded hover:bg-green-600/80 transition-all duration-200"
              >
                测试Y显示
              </button>
              <button
                @click="testYValues"
                class="px-2 py-1 text-xs bg-cyan-600/60 border border-cyan-500 text-cyan-300 rounded hover:bg-cyan-600/80 transition-all duration-200"
              >
                测试Y高度
              </button>
              <button
                @click="testDelayQueue"
                class="px-2 py-1 text-xs bg-indigo-600/60 border border-indigo-500 text-indigo-300 rounded hover:bg-indigo-600/80 transition-all duration-200"
              >
                测试队列
              </button>
              <button
                @click="testSmoothAnimation"
                class="px-2 py-1 text-xs bg-emerald-600/60 border border-emerald-500 text-emerald-300 rounded hover:bg-emerald-600/80 transition-all duration-200"
              >
                测试流畅
              </button>
              <button
                @click="testDataFlow"
                class="px-2 py-1 text-xs bg-pink-600/60 border border-pink-500 text-pink-300 rounded hover:bg-pink-600/80 transition-all duration-200"
              >
                调试数据流
              </button>
            </div>
          </div> -->

          <!-- 重置按钮 -->
          <!-- <div class="pt-2 border-t border-purple-500/20">
            <button
              @click="resetToDefaults"
              class="w-full px-4 py-2 text-xs bg-slate-600/60 border border-slate-500 text-slate-300 rounded hover:bg-slate-600/80 transition-all duration-200"
            >
              重置为默认
            </button>
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Futuristic card style */
.futuristic-card {
  clip-path: polygon(1.5rem 0, 100% 0, 100% calc(100% - 1.5rem), calc(100% - 1.5rem) 100%, 0 100%, 0 1.5rem);
}

/* 自定义滑块样式 */
.slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  background: #a855f7;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 8px rgba(168, 85, 247, 0.5);
}

.slider::-webkit-slider-track {
  background: rgba(51, 65, 85, 0.6);
  height: 8px;
  border-radius: 4px;
}

.slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #a855f7;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 8px rgba(168, 85, 247, 0.5);
}

.slider::-moz-range-track {
  background: rgba(51, 65, 85, 0.6);
  height: 8px;
  border-radius: 4px;
  border: none;
}
</style>
