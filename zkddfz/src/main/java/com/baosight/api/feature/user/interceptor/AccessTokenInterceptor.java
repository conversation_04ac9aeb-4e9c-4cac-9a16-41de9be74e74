package com.baosight.api.feature.user.interceptor;

import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.servlet.HandlerInterceptor;

import com.baosight.api.db.user.UserEntity;
import com.baosight.api.db.user.UserRepository;
import com.baosight.api.feature.user.security.AccessTokenService;
import com.baosight.api.feature.user.security.UserContextService;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class AccessTokenInterceptor implements HandlerInterceptor {
  private final AccessTokenService accessTokenService;

  private final UserRepository userRepository;

  private final UserContextService userContextService;

  private String extractAccessTokenFromRequest(HttpServletRequest request) {
    Cookie[] cookies = request.getCookies();
    if (cookies != null) {
      for (Cookie cookie : cookies) {
        if (accessTokenService.getAccessTokenCookieName().equals(cookie.getName())) {
          return cookie.getValue();
        }
      }
    }

    throw new ResponseStatusException(HttpStatus.UNAUTHORIZED);
  }

  @Override
  public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
      @NonNull Object handler)
      throws Exception {
    String token = extractAccessTokenFromRequest(request);

    String username = accessTokenService.validateAccessToken(token);

    UserEntity user = userRepository.findByUsername(username)
        .orElseThrow(() -> new ResponseStatusException(HttpStatus.UNAUTHORIZED));

    userContextService.setUserAttribute(request, user);

    return true;
  }
}
