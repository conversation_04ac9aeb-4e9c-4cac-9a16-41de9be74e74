import mitt from 'mitt';
import { onMounted, onUnmounted } from 'vue';
import type { EventsApp } from './app';

type Events = EventsApp;

/**
 * 广播事件：emitter.emit(event, payload)
 */
export const emitter = mitt<Events>();

/**
 * 注册事件（Vue 组合式函数）：useEmitter(event, handler)
 */
export const useEmitter = <T extends keyof Events>(event: T, handler: (payload: Events[T]) => void) => {
  onMounted(() => {
    emitter.on(event, handler);
  });
  onUnmounted(() => {
    emitter.off(event, handler);
  });
};
