import { api } from '@/common/api';
import { ApiPaths } from '@/constants/api-paths';
import { useUserStore } from '@/stores/user';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

interface SessionDTO {
  username: string;
  displayName: string;
  roles: string[];
}

type Session = SessionDTO;

const getSession = async (): Promise<Session> => {
  const { data } = await api.post<SessionDTO>(ApiPaths.User.SESSION);
  return data;
};

export const useAuth = () => {
  const user = useUserStore();

  const router = useRouter();

  const isPending = ref(true);

  const updateUserBySession = (session: Session) => {
    user.username = session.username;
    user.displayName = session.displayName;
    user.roles = session.roles;
  };

  onMounted(async () => {
    try {
      if (location.pathname === '/login') {
        return;
      }

      // 绕过登录验证 - 直接设置模拟用户信息
      console.log('🚀 绕过登录验证，直接进入系统');
      updateUserBySession({
        username: 'demo_user',
        displayName: '演示用户',
        roles: ['管理员'] // 设置为管理员角色以访问所有功能
      });

      // 注释掉原来的认证检查
      // updateUserBySession(await getSession());
    } catch (error) {
      // 即使出错也不跳转到登录页面
      console.warn('认证检查出错，但已绕过:', error);

      // 设置默认用户信息
      updateUserBySession({
        username: 'guest_user',
        displayName: '访客用户',
        roles: ['管理员']
      });

      // 注释掉跳转到登录页面的逻辑
      // router.replace({ name: 'login' });
    } finally {
      isPending.value = false;
    }
  });

  return { isPending };
};
