<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车辆类型测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .vehicle-type {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .type-code {
            font-weight: bold;
            color: #007bff;
            font-size: 18px;
        }
        .vehicle-name {
            font-size: 16px;
            color: #333;
            margin: 5px 0;
        }
        .model-path {
            font-size: 12px;
            color: #666;
            font-family: monospace;
        }
        .sample-id {
            background: #e7f3ff;
            padding: 8px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            font-weight: bold;
        }
        .new-vehicle {
            background: #e8f5e8;
            border-color: #4caf50;
        }
        .new-label {
            background: #4caf50;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 10px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        h2 {
            color: #555;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚗 车辆类型配置测试</h1>
        
        <h2>📋 所有车辆类型及其代码映射</h2>
        
        <div class="vehicle-type">
            <div class="type-code">MBC → 移动转载机 (Mobile Transfer Machine)</div>
            <div class="model-path">模型路径: /models/new/移动转载机ok.glb</div>
            <div class="sample-id">示例ID: MBC_12345</div>
        </div>

        <div class="vehicle-type">
            <div class="type-code">BBC → 桥式转载机 (Bridge Transfer Machine)</div>
            <div class="model-path">模型路径: /models/new/桥式转载机ok.glb</div>
            <div class="sample-id">示例ID: BBC_67890</div>
        </div>

        <div class="vehicle-type">
            <div class="type-code">TBC → 中继转载机 (Relay Transfer Machine)</div>
            <div class="model-path">模型路径: /models/new/中继转载机-B-ok.glb</div>
            <div class="sample-id">示例ID: TBC_54321</div>
        </div>

        <div class="vehicle-type">
            <div class="type-code">BWE → 3500T斗轮挖掘机 (3500T Bucket Wheel Excavator)</div>
            <div class="model-path">模型路径: /models/new/3500T斗轮挖掘机.glb</div>
            <div class="sample-id">示例ID: BWE_98765</div>
        </div>

        <div class="vehicle-type">
            <div class="type-code">RSC → 大型布料机 (Large Spreader)</div>
            <div class="model-path">模型路径: /models/new/大型布料机ok-6.glb</div>
            <div class="sample-id">示例ID: RSC_13579</div>
        </div>

        <div class="vehicle-type new-vehicle">
            <div class="type-code">PCC → 移动供电车 (Mobile Power Supply Vehicle) <span class="new-label">新增</span></div>
            <div class="model-path">模型路径: /models/new/移动供电车ok.glb</div>
            <div class="sample-id">示例ID: PCC_24680</div>
        </div>

        <div class="vehicle-type">
            <div class="type-code">RSCM → 扇形布料机 (Fan-shaped Spreader)</div>
            <div class="model-path">模型路径: /models/new/扇形布料机ok.glb</div>
            <div class="sample-id">示例ID: RSCM_11111</div>
        </div>

        <div class="vehicle-type">
            <div class="type-code">BWEM → 1500T斗轮挖掘机 (1500T Bucket Wheel Excavator)</div>
            <div class="model-path">模型路径: /models/new/1500T斗轮挖掘机.glb</div>
            <div class="sample-id">示例ID: BWEM_22222</div>
        </div>

        <div class="vehicle-type new-vehicle">
            <div class="type-code">MDF → 移动分料漏斗 (Mobile Distribution Funnel) <span class="new-label">新增</span></div>
            <div class="model-path">模型路径: /models/new/移动转载机ok.glb</div>
            <div class="sample-id">示例ID: MDF_33333</div>
        </div>

        <h2>🔧 实现的功能</h2>
        <ul>
            <li>✅ 新增了两种车辆类型：移动供电车(PCC) 和 移动分料漏斗(MDF)</li>
            <li>✅ 更新了车辆ID生成系统，使用格式 {CODE}_{5位随机数}</li>
            <li>✅ 为每个车辆对象添加了 type 属性，包含车辆类型代码</li>
            <li>✅ 更新了WebSocket集成，type字段会包含在实时数据广播中</li>
            <li>✅ 保持了与现有功能的兼容性</li>
        </ul>

        <h2>📡 WebSocket 数据结构示例</h2>
        <pre style="background: #f4f4f4; padding: 15px; border-radius: 5px; overflow-x: auto;">
{
  "device_id": "PCC_12311",
  "position": {
    "x": 905,
    "y": 0,
    "z": -970
  },
  "rotation": {
    "x": 0,
    "y": 90,
    "z": 0
  },
  "duration": 1,
  "power": 85,
  "consume": 15,
  "type": "PCC"
}
        </pre>

        <h2>🧪 测试说明</h2>
        <p>要测试新功能，请：</p>
        <ol>
            <li>打开路线仿真页面</li>
            <li>点击"添加设备"按钮</li>
            <li>选择新增的车辆类型（移动供电车或移动分料漏斗）</li>
            <li>填写设备信息并确认添加</li>
            <li>检查生成的设备ID是否符合新格式（如 PCC_12345）</li>
            <li>通过WebSocket监控确认type字段是否正确传输</li>
        </ol>
    </div>
</body>
</html>
