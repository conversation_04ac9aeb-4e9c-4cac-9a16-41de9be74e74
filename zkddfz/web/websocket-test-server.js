const WebSocket = require('ws');

// 创建WebSocket服务器
const wss = new WebSocket.Server({ port: 8080 });

console.log('🚀 WebSocket测试服务器启动在端口 8080');

// 存储连接的客户端
const clients = new Set();

// 模拟车辆控制电文数据
const sampleVehicleCommands = [
  {
    "device_id": "device_22924",
    "position": {
      "x": 905,
      "y": 0,
      "z": -970
    },
    "rotation": {
      "x": 0,
      "y": 90,
      "z": 0
    },
    "duration": 0,
    "power": 85,
    "consume": 15
  },
  {
    "device_id": "device_12345",
    "position": {
      "x": 800,
      "y": 0,
      "z": -800
    },
    "rotation": {
      "x": 0,
      "y": 45,
      "z": 0
    },
    "duration": 0,
    "power": 92,
    "consume": 8
  }
];

wss.on('connection', function connection(ws) {
  console.log('🔗 新的WebSocket连接建立');
  clients.add(ws);

  // 发送欢迎消息
  ws.send(JSON.stringify({
    type: 'connection',
    action: 'connected',
    data: {
      message: '欢迎连接到WebSocket测试服务器',
      timestamp: Date.now()
    }
  }));

  // 处理接收到的消息
  ws.on('message', function incoming(message) {
    try {
      const data = JSON.parse(message);
      console.log('📨 接收到消息:', data);

      // 如果是车辆状态信息（数组格式）
      if (Array.isArray(data)) {
        console.log('📊 接收到车辆状态信息:', data.length, '辆车');
        data.forEach(vehicle => {
          console.log(`  - 车辆 ${vehicle.device_id}: 位置(${vehicle.position.x}, ${vehicle.position.y}, ${vehicle.position.z}), 电量: ${vehicle.power}%, 消耗: ${vehicle.consume}`);
        });
      } else {
        // 处理其他类型的消息
        switch (data.type) {
          case 'test':
            console.log('🧪 收到测试消息:', data.data.message);
            break;
          case 'request':
            if (data.action === 'route_data') {
              console.log('📤 发送路线数据请求响应');
              ws.send(JSON.stringify({
                type: 'route_data',
                action: 'batch',
                data: sampleVehicleCommands
              }));
            }
            break;
          default:
            console.log('❓ 未知消息类型:', data.type);
        }
      }
    } catch (error) {
      console.error('消息解析失败:', error);
    }
  });

  // 处理连接关闭
  ws.on('close', function close() {
    console.log('🔌 WebSocket连接关闭');
    clients.delete(ws);
  });

  // 处理连接错误
  ws.on('error', function error(err) {
    console.error('WebSocket连接错误:', err);
    clients.delete(ws);
  });
});

// 定时发送车辆控制命令（模拟后端发送控制电文）
setInterval(() => {
  if (clients.size > 0) {
    // 随机修改车辆位置
    const commands = sampleVehicleCommands.map(cmd => ({
      ...cmd,
      position: {
        x: cmd.position.x + (Math.random() - 0.5) * 20,
        y: cmd.position.y,
        z: cmd.position.z + (Math.random() - 0.5) * 20
      },
      rotation: {
        x: cmd.rotation.x,
        y: cmd.rotation.y + (Math.random() - 0.5) * 30,
        z: cmd.rotation.z
      },
      power: Math.max(0, cmd.power - Math.random() * 2), // 电量逐渐减少
      consume: cmd.consume + Math.random() * 1 // 消耗逐渐增加
    }));

    // 广播给所有连接的客户端
    clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify(commands));
        console.log('📤 发送车辆控制命令给客户端');
      }
    });
  }
}, 5000); // 每5秒发送一次控制命令

console.log('📋 测试说明:');
console.log('1. 前端连接到 ws://localhost:8080');
console.log('2. 服务器每5秒发送一次车辆控制电文');
console.log('3. 前端每秒发送车辆状态信息');
console.log('4. 可以发送测试消息和请求路线数据');
