import DayjsExtended from 'dayjs';
import 'dayjs/locale/zh-cn';
import duration from 'dayjs/plugin/duration';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import relativeTime from 'dayjs/plugin/relativeTime';
import utc from 'dayjs/plugin/utc';
import weekOfYear from 'dayjs/plugin/weekOfYear';

DayjsExtended.locale('zh-cn');
DayjsExtended.extend(duration);
DayjsExtended.extend(isSameOrBefore);
DayjsExtended.extend(quarterOfYear);
DayjsExtended.extend(relativeTime);
DayjsExtended.extend(utc);
DayjsExtended.extend(weekOfYear);

/**
 * 加入扩展后的 dayjs，包括中文扩展、UTC 扩展、季度扩展等常用扩展
 */
export const dayjs = DayjsExtended;
