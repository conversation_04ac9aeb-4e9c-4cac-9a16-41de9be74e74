package com.baosight.api.feature.user.route.session;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baosight.api.constants.ApiPaths;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
public class SessionController {
  private final SessionService sessionService;

  @PostMapping(ApiPaths.User.SESSION)
  public SessionResponseBody handleRequest(HttpServletRequest request) {
    return sessionService.getSessionResponseBody(request);
  }
}
