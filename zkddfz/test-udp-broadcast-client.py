#!/usr/bin/env python3
"""
UDP广播客户端测试程序
用于测试接收从WebSocket服务器广播的车辆实时数据
"""

import socket
import json
import threading
import time
from datetime import datetime

class UdpBroadcastClient:
    def __init__(self, listen_port=9091, buffer_size=4096):
        self.listen_port = listen_port
        self.buffer_size = buffer_size
        self.socket = None
        self.running = False
        self.received_count = 0
        
    def start_listening(self):
        """开始监听UDP广播"""
        try:
            # 创建UDP socket
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            
            # 绑定到指定端口，监听所有接口
            self.socket.bind(('', self.listen_port))
            self.running = True
            
            print(f"🚀 UDP广播客户端启动成功，监听端口: {self.listen_port}")
            print(f"📡 等待接收车辆数据广播...")
            print("-" * 80)
            
            while self.running:
                try:
                    # 接收UDP数据
                    data, addr = self.socket.recvfrom(self.buffer_size)
                    self.handle_received_data(data, addr)
                    
                except socket.timeout:
                    continue
                except socket.error as e:
                    if self.running:
                        print(f"❌ Socket错误: {e}")
                    break
                    
        except Exception as e:
            print(f"❌ 启动UDP客户端失败: {e}")
        finally:
            self.cleanup()
    
    def handle_received_data(self, data, addr):
        """处理接收到的数据"""
        try:
            # 解码数据
            json_str = data.decode('utf-8')
            
            # 尝试解析JSON
            vehicle_data = json.loads(json_str)
            
            self.received_count += 1
            timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
            
            print(f"📨 [{timestamp}] 收到来自 {addr[0]}:{addr[1]} 的数据 (#{self.received_count})")
            
            # 如果是车辆数据数组
            if isinstance(vehicle_data, list):
                print(f"🚗 车辆数量: {len(vehicle_data)}")
                for i, vehicle in enumerate(vehicle_data):
                    self.print_vehicle_info(vehicle, i + 1)
            else:
                # 单个车辆数据或其他格式
                print(f"📄 数据类型: {type(vehicle_data).__name__}")
                if isinstance(vehicle_data, dict) and 'device_id' in vehicle_data:
                    self.print_vehicle_info(vehicle_data, 1)
                else:
                    print(f"📄 数据内容: {json.dumps(vehicle_data, indent=2, ensure_ascii=False)}")
            
            print("-" * 80)
            
        except json.JSONDecodeError:
            # 不是JSON格式的数据
            print(f"📄 收到非JSON数据: {data.decode('utf-8', errors='ignore')}")
        except Exception as e:
            print(f"❌ 处理数据时出错: {e}")
    
    def print_vehicle_info(self, vehicle, index):
        """打印车辆信息"""
        try:
            device_id = vehicle.get('device_id', 'Unknown')
            position = vehicle.get('position', {})
            rotation = vehicle.get('rotation', {})
            power = vehicle.get('power', 0)
            consume = vehicle.get('consume', 0)
            vehicle_type = vehicle.get('type', 'Unknown')
            
            print(f"  🚗 车辆 {index}: {device_id} (类型: {vehicle_type})")
            print(f"     📍 位置: X={position.get('x', 0)}, Y={position.get('y', 0)}, Z={position.get('z', 0)}")
            print(f"     🔄 角度: X={rotation.get('x', 0)}°, Y={rotation.get('y', 0)}°, Z={rotation.get('z', 0)}°")
            print(f"     🔋 电量: {power}%, 消耗: {consume}")
            
        except Exception as e:
            print(f"     ❌ 解析车辆信息出错: {e}")
    
    def stop_listening(self):
        """停止监听"""
        self.running = False
        if self.socket:
            self.socket.close()
    
    def cleanup(self):
        """清理资源"""
        if self.socket:
            self.socket.close()
        print(f"\n✅ UDP广播客户端已停止，共接收 {self.received_count} 条消息")

def main():
    """主函数"""
    print("🔧 UDP广播客户端测试程序")
    print("=" * 80)
    
    # 创建客户端
    client = UdpBroadcastClient()
    
    try:
        # 在单独线程中启动监听
        listen_thread = threading.Thread(target=client.start_listening)
        listen_thread.daemon = True
        listen_thread.start()
        
        # 主线程等待用户输入
        print("\n💡 提示:")
        print("   - 确保WebSocket服务器正在运行 (端口8080)")
        print("   - 确保有WebSocket客户端发送车辆状态数据")
        print("   - 按 Ctrl+C 退出程序")
        print("\n⌨️  按回车键查看统计信息，输入 'quit' 退出:")
        
        while True:
            user_input = input().strip().lower()
            if user_input == 'quit':
                break
            elif user_input == '':
                print(f"📊 统计信息: 已接收 {client.received_count} 条消息")
            else:
                print("❓ 未知命令，按回车查看统计，输入 'quit' 退出")
                
    except KeyboardInterrupt:
        print("\n🛑 收到中断信号，正在退出...")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
    finally:
        client.stop_listening()

if __name__ == "__main__":
    main()
