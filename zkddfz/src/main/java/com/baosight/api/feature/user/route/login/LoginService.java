package com.baosight.api.feature.user.route.login;

import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import com.baosight.api.db.user.UserEntity;
import com.baosight.api.db.user.UserRepository;
import com.baosight.api.feature.user.security.PasswordService;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class LoginService {
  private final UserRepository userRepository;

  private final PasswordService passwordService;

  public void checkUserCanLogin(LoginRequestBody loginRequestBody) {
    UserEntity user = userRepository.findByUsername(loginRequestBody.getUsername())
        .orElseThrow(() -> new ResponseStatusException(HttpStatus.UNPROCESSABLE_ENTITY, "用户不存在"));

    if (!passwordService.matches(loginRequestBody.getPassword(), user.getPasswordHash())) {
      throw new ResponseStatusException(HttpStatus.UNPROCESSABLE_ENTITY, "密码错误");
    }
  }
}
