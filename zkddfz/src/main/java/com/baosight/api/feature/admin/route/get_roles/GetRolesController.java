package com.baosight.api.feature.admin.route.get_roles;

import java.util.List;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baosight.api.constants.ApiPaths;
import com.baosight.api.db.role.RoleEntity;
import com.baosight.api.db.role.RoleRepository;

import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
public class GetRolesController {
  private final RoleRepository roleRepository;

  @PostMapping(ApiPaths.Admin.GET_ROLES)
  public List<RoleEntity> handleRequest() {
    return roleRepository.findAll();
  }
}
