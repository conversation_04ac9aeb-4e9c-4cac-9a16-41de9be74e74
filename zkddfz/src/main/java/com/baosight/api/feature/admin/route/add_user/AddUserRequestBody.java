package com.baosight.api.feature.admin.route.add_user;

import java.util.List;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class AddUserRequestBody {
  @NotBlank(message = "用户名不能为空")
  private String username;

  @NotBlank(message = "姓名不能为空")
  private String displayName;

  @NotBlank(message = "密码不能为空")
  @Size(min = 6, message = "密码长度不能小于 6 个字符")
  private String password;

  @NotNull(message = "角色列表不能为空")
  private List<String> roles;
}
