<template>
  <div class="flex gap-1">
    <router-link v-if="isAdmin" :to="{ name: 'route_simulatton' }">
      <!-- class="min-w-16 h-10 px-1 flex justify-center" -->
      <!-- :class="composeButtonClass( (currentRoute.name as string).startsWith('route_simulatton'))" -->
      <button class="btn">
        <div
          class="btn_sr"
          :class="
            (currentRoute.name as string).startsWith('route_simulatton') ? 'activeClass_h_l1' : 'btn_sr_l1'
          "
        >
          <!-- <div class="flex flex-col items-center text-sm"> -->
          <!-- <SafetyOutlined /> -->
          <div :class="composeButtonClass( (currentRoute.name as string).startsWith('route_simulatton'))">路线模拟</div>
        </div>
      </button>
    </router-link>
    <router-link v-if="isAdmin" :to="{ name: 'coordinate_system' }">
      <!-- class="min-w-16 h-10 px-1 flex justify-center" -->
      <!-- :class="composeButtonClass( (currentRoute.name as string).startsWith('coordinate_system'))" -->
      <button class="btn">
        <div
          class="btn_sr"
          :class="
            (currentRoute.name as string).startsWith('coordinate_system') ? 'activeClass_h_l2' : 'btn_sr_l2'
          "
        >
          <!-- <div class="flex flex-col items-center text-sm">  -->
          <!-- <SafetyOutlined /> -->
          <div :class="composeButtonClass( (currentRoute.name as string).startsWith('coordinate_system'))">
            坐标系统
          </div>
        </div>
      </button>
    </router-link>

    <!-- <router-link v-if="isAdmin" :to="{ name: 'admin' }">
      <a-button
        class="min-w-16 h-10 px-1 flex justify-center"
        type="text"
        :class="composeButtonClass( (currentRoute.name as string).startsWith('admin'))"
      >
        <div class="flex flex-col items-center text-sm">
          <SafetyOutlined />
          <div>管理员</div>
        </div>
      </a-button>
    </router-link> -->

    <!-- <menu-system /> -->
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/user';
import { storeToRefs } from 'pinia';
import { useRouter } from 'vue-router';

const userStore = useUserStore();
const { isAdmin } = storeToRefs(userStore);

const router = useRouter();
const { currentRoute } = router;

const composeButtonClass = (selected: boolean) => {
  return [selected ? 'activeClass_h_l1_txt' : 'color_xtx'];
};
</script>
<style scoped>
.btn {
  font-size: 18px;
  font-weight: 800;
  /* margin-left: 10px; */
  padding: 0 !important;
  border: none;
  cursor: pointer;
  background: rgba(0, 0, 0, 0) no-repeat;
}

.btn_sr {
  display: inline-block;
  width: 190px;
  font-size: 20px;
  font-weight: 800;
  padding: 10px 25px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.color_xtx {
  background-image: linear-gradient(rgb(255, 255, 255), rgb(171, 228, 255));
  -webkit-background-clip: text;
  color: transparent;
}
.btn_sr_l1 {
  background-image: url('../../assets/img/L1btn_Blue.png');
}
.btn_sr_l2 {
  background-image: url('../../assets/img/L2btn_Blue.png');
}
.activeClass_h_l1 {
  background-image: url('../../assets/img/L1btn_Yellow.png');
}
.activeClass_h_l2 {
  background-image: url('../../assets/img/L2btn_Yellow.png');
}
.activeClass_h_l1_txt {
  color: rgb(254, 214, 55);
}
</style>
