import { dayjs } from '@/common/dayjs';
import type { GridApi, GridReadyEvent, IDateFilterParams, ValueFormatterParams } from 'ag-grid-community';
import { ref } from 'vue';

/**
 * 导出 AgGrid Vue 组件
 */
export { default as AgGrid } from './ag-grid.vue';

/**
 * 日期比较器（用于 agDateColumnFilter）
 *
 * 参考：https://www.ag-grid.com/javascript-data-grid/filter-date/
 */
export const dateFilterParams: IDateFilterParams = {
  comparator: (filterLocalDateAtMidnight: Date, cellValue: string) => {
    const filterDateFormatted = dayjs(filterLocalDateAtMidnight, 'YYYY-MM-DD').format('YYYY-MM-DD');
    const cellValueFormatted = dayjs(cellValue).format('YYYY-MM-DD');

    if (dayjs(filterDateFormatted).isBefore(cellValueFormatted)) {
      return -1;
    }

    if (dayjs(filterDateFormatted).isAfter(cellValueFormatted)) {
      return 1;
    }

    return 0;
  },
};

/**
 * 格式化日期
 * @param params ValueFormatterParams ag-grid
 * @param format? 日期格式，默认 YYYY-MM-DD HH:mm:ss
 */
export const dateTimeValueFormatter = (params: ValueFormatterParams, format = 'YYYY-MM-DD HH:mm:ss') => {
  return params.value && dayjs(params.value).isValid() ? dayjs(params.value).format(format) : '';
};

/**
 * 初始化 gridApi, 一般和 \@grid-ready 配合
 * @example [gridApi, setGridApi] = useGridApi()
 */
export const useGridApi = <TData = any>() => {
  const gridApi = ref<GridApi<TData>>();
  const setGridApi = (event: GridReadyEvent) => {
    gridApi.value = event.api;
  };
  return [gridApi, setGridApi] as const;
};
