package com.baosight.api.init;

import java.io.File;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import com.baosight.api.util.SpringProfileChecker;

import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class SystemInfoPrinter {
  private static final Logger logger = LoggerFactory.getLogger(SystemInfoPrinter.class);

  private final SpringProfileChecker springProfileChecker;

  @Value("${server.port}")
  private int port;

  @EventListener(ApplicationReadyEvent.class)
  public void printSystemInfo() {
    printJvmInfo();
    printMavenInfo();
    printApplicationInfo();
  }

  private void printApplicationInfo() {
    logger.info("Active Profile: {}", springProfileChecker.getCurrentProfile());
    logger.info("Server Port: {}", port);
    logger.info("Spring Boot 已成功启动!");
  }

  private void printMavenInfo() {
    String classpathRepo = extractRepositoryFromClasspath();
    if (classpathRepo != null) {
      logger.info("Active Maven Repository: {}", classpathRepo);
    } else {
      logger.warn("No Active Maven Repository Found!");
    }
  }

  private String extractRepositoryFromClasspath() {
    String classpath = System.getProperty("java.class.path");
    String[] classpathEntries = classpath.split(File.pathSeparator);

    for (String path : classpathEntries) {
      if (path.contains(".m2") && path.contains("repository")) {
        int repoIndex = path.indexOf("repository");
        if (repoIndex != -1) {
          String repoPath = path.substring(0, repoIndex + "repository".length());
          return repoPath;
        }
      }
    }

    return null;
  }

  private void printJvmInfo() {
    if (!logger.isInfoEnabled()) {
      return;
    }

    logger.info("Java Version: {}", System.getProperty("java.version"));
    logger.info("Java Vendor: {}", System.getProperty("java.vendor"));
    logger.info("Java Runtime Version: {}", System.getProperty("java.runtime.version"));
    logger.info("JVM Name: {}", System.getProperty("java.vm.name"));
    logger.info("Java Home: {}", System.getProperty("java.home"));
  }
}
