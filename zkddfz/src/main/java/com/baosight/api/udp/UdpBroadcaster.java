package com.baosight.api.udp;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * UDP广播器
 * 用于将车辆实时数据通过UDP广播给外部系统
 */
@Component
public class UdpBroadcaster {

    private static final Logger logger = LoggerFactory.getLogger(UdpBroadcaster.class);

    @Value("${udp.broadcast.port:9091}")
    private int broadcastPort;

    @Value("${udp.broadcast.address:127.0.0.1}")
    private String broadcastAddress;

    @Value("${udp.broadcast.enabled:true}")
    private boolean broadcastEnabled;

    private DatagramSocket socket;
    private final AtomicBoolean initialized = new AtomicBoolean(false);
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 存储订阅的客户端地址
    private final List<InetSocketAddress> subscribers = new CopyOnWriteArrayList<>();

    /**
     * 初始化UDP广播器
     */
    public void initialize() {
        if (initialized.get()) {
            logger.debug("UDP广播器已经初始化");
            return;
        }

        if (!broadcastEnabled) {
            logger.info("UDP广播功能已禁用");
            return;
        }

        try {
            socket = new DatagramSocket();
            socket.setBroadcast(true); // 启用广播
            initialized.set(true);
            logger.info("🚀 UDP广播器初始化成功，广播地址: {}:{}", broadcastAddress, broadcastPort);
        } catch (SocketException e) {
            logger.error("UDP广播器初始化失败: {}", e.getMessage(), e);
            initialized.set(false);
        }
    }

    /**
     * 广播车辆数据
     * @param vehicleData 车辆数据列表
     */
    public void broadcastVehicleData(List<?> vehicleData) {
        if (!initialized.get() || !broadcastEnabled) {
            return;
        }

        try {
            // 将车辆数据序列化为JSON
            String jsonData = objectMapper.writeValueAsString(vehicleData);
            byte[] data = jsonData.getBytes(StandardCharsets.UTF_8);

            // 广播到默认地址
            broadcastToAddress(data, broadcastAddress, broadcastPort);

            // 广播到订阅的客户端
            for (InetSocketAddress subscriber : subscribers) {
                broadcastToAddress(data, subscriber.getAddress().getHostAddress(), subscriber.getPort());
            }

            logger.debug("📡 UDP广播车辆数据完成，数据长度: {} 字节，订阅者数量: {}", 
                data.length, subscribers.size());

        } catch (Exception e) {
            logger.error("UDP广播车辆数据失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 广播单个车辆数据
     * @param vehicleData 单个车辆数据
     */
    public void broadcastSingleVehicleData(Object vehicleData) {
        if (!initialized.get() || !broadcastEnabled) {
            return;
        }

        try {
            // 将车辆数据序列化为JSON
            String jsonData = objectMapper.writeValueAsString(vehicleData);
            byte[] data = jsonData.getBytes(StandardCharsets.UTF_8);

            // 广播到默认地址
            broadcastToAddress(data, broadcastAddress, broadcastPort);

            // 广播到订阅的客户端
            for (InetSocketAddress subscriber : subscribers) {
                broadcastToAddress(data, subscriber.getAddress().getHostAddress(), subscriber.getPort());
            }

            logger.debug("📡 UDP广播单个车辆数据完成，数据长度: {} 字节", data.length);

        } catch (Exception e) {
            logger.error("UDP广播单个车辆数据失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 广播原始数据
     * @param rawData 原始数据字符串
     */
    public void broadcastRawData(String rawData) {
        if (!initialized.get() || !broadcastEnabled) {
            return;
        }

        try {
            byte[] data = rawData.getBytes(StandardCharsets.UTF_8);

            // 广播到默认地址
            broadcastToAddress(data, broadcastAddress, broadcastPort);

            // 广播到订阅的客户端
            for (InetSocketAddress subscriber : subscribers) {
                broadcastToAddress(data, subscriber.getAddress().getHostAddress(), subscriber.getPort());
            }

            logger.debug("📡 UDP广播原始数据完成，数据长度: {} 字节", data.length);

        } catch (Exception e) {
            logger.error("UDP广播原始数据失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 添加订阅者
     * @param address 订阅者地址
     * @param port 订阅者端口
     */
    public void addSubscriber(String address, int port) {
        try {
            InetSocketAddress subscriber = new InetSocketAddress(InetAddress.getByName(address), port);
            if (!subscribers.contains(subscriber)) {
                subscribers.add(subscriber);
                logger.info("➕ 添加UDP广播订阅者: {}:{}", address, port);
            }
        } catch (UnknownHostException e) {
            logger.error("添加订阅者失败，无效地址: {}:{}", address, port);
        }
    }

    /**
     * 移除订阅者
     * @param address 订阅者地址
     * @param port 订阅者端口
     */
    public void removeSubscriber(String address, int port) {
        try {
            InetSocketAddress subscriber = new InetSocketAddress(InetAddress.getByName(address), port);
            if (subscribers.remove(subscriber)) {
                logger.info("➖ 移除UDP广播订阅者: {}:{}", address, port);
            }
        } catch (UnknownHostException e) {
            logger.error("移除订阅者失败，无效地址: {}:{}", address, port);
        }
    }

    /**
     * 获取订阅者数量
     */
    public int getSubscriberCount() {
        return subscribers.size();
    }

    /**
     * 向指定地址广播数据
     */
    private void broadcastToAddress(byte[] data, String address, int port) {
        if (socket == null || socket.isClosed()) {
            return;
        }

        try {
            InetAddress targetAddress = InetAddress.getByName(address);
            DatagramPacket packet = new DatagramPacket(data, data.length, targetAddress, port);
            socket.send(packet);
            
            logger.debug("📤 UDP数据已发送到 {}:{}, 长度: {} 字节", address, port, data.length);
        } catch (Exception e) {
            logger.error("向 {}:{} 发送UDP数据失败: {}", address, port, e.getMessage());
        }
    }

    /**
     * 关闭UDP广播器
     */
    public void shutdown() {
        if (initialized.get()) {
            logger.info("🛑 正在关闭UDP广播器...");
            initialized.set(false);
            
            if (socket != null && !socket.isClosed()) {
                socket.close();
                logger.info("✅ UDP广播器已关闭");
            }
            
            subscribers.clear();
        }
    }

    /**
     * 检查UDP广播器是否已初始化
     */
    public boolean isInitialized() {
        return initialized.get() && socket != null && !socket.isClosed();
    }

    /**
     * 获取广播配置信息
     */
    public String getBroadcastInfo() {
        return String.format("UDP广播器 - 地址: %s:%d, 启用: %s, 订阅者: %d", 
            broadcastAddress, broadcastPort, broadcastEnabled, subscribers.size());
    }
}
