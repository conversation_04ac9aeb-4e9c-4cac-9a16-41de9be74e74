<template>
  <a-dropdown>
    <template #overlay>
      <a-menu>
        <div class="px-3 space-y-1">
          <div>账号：{{ username }}</div>
          <div>姓名：{{ displayName }}</div>
          <div>角色：{{ roles.join() }}</div>
        </div>

        <a-divider class="my-1" />

        <a-menu-item @click="toggleColorScheme">
          <div class="flex items-center space-x-3">
            <template v-if="isDark">
              <div><SunIcon class="size-4" /></div>
              <div>切换浅色</div>
            </template>
            <template v-else>
              <div><MoonIcon class="size-4" /></div>
              <div>切换深色</div>
            </template>
          </div>
        </a-menu-item>

        <a-menu-item @click="logout">
          <div class="flex items-center space-x-3">
            <div><LogoutOutlined class="size-4" /></div>
            <div>退出登录</div>
          </div>
        </a-menu-item>
      </a-menu>
    </template>

    <a-button type="text" class="min-w-16 h-10 px-1 flex justify-center">
      <div class="flex flex-col items-center text-sm">
        <SettingOutlined />
        <div>系统</div>
      </div>
    </a-button>
  </a-dropdown>
</template>

<script setup lang="ts">
import { api } from '@/common/api';
import { ApiPaths } from '@/constants/api-paths';
import { useUserStore } from '@/stores/user';
import { LogoutOutlined, SettingOutlined } from '@ant-design/icons-vue';
import { MoonIcon, SunIcon } from '@heroicons/vue/24/outline';
import { useColorMode, useDark } from '@vueuse/core';
import { storeToRefs } from 'pinia';

const isDark = useDark();

const colorMode = useColorMode();

const toggleColorScheme = () => {
  if (colorMode.value !== 'light') {
    colorMode.value = 'light';
  } else {
    colorMode.value = 'dark';
  }
};

const user = useUserStore();
const { displayName, username, roles } = storeToRefs(user);

const logout = async () => {
  try {
    await api.post(ApiPaths.User.LOGOUT);
  } finally {
    localStorage.clear();
    location.replace('/login');
  }
};
</script>
