package com.baosight.api.feature.user.security;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

@Service
public class PasswordService {
  private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

  public boolean matches(String rawPassword, String encodedPassword) {
    return passwordEncoder.matches(rawPassword, encodedPassword);
  }

  public String hash(String rawPassword) {
    return passwordEncoder.encode(rawPassword);
  }
}
