<template>
  <app-drawer :id="id" :title="title" @open="handleOpen">
    <form class="space-y-4" @submit.prevent="handleSubmit">
      <div class="space-y-2">
        <label for="upsert-username">用户名</label>
        <a-input :disabled="edit" id="upsert-username" v-model:value="username" />
      </div>

      <div class="space-y-2">
        <label for="upsert-displayName">姓名</label>
        <a-input id="upsert-displayName" v-model:value="displayName" />
      </div>

      <div class="space-y-2">
        <label for="upsert-password">密码</label>
        <a-input id="upsert-password" v-model:value="password" type="password" />
      </div>

      <div class="space-y-2">
        <label for="upsert-roles">角色</label>
        <a-select
          :options="roles?.map(value => ({ value }))"
          class="w-full"
          id="upsert-roles"
          mode="multiple"
          v-model:value="userRoles"
        >
        </a-select>
      </div>

      <div>
        <a-button block type="primary" html-type="submit">确认</a-button>
      </div>
    </form>
  </app-drawer>
</template>

<script setup lang="ts">
import { api } from '@/common/api';
import { AppDrawer } from '@/components/drawer';
import { ApiPaths } from '@/constants/api-paths';
import { emitter } from '@/emitter';
import { useQueryClient } from '@tanstack/vue-query';
import { message } from 'ant-design-vue';
import { computed, ref } from 'vue';
import { getRoles, getUserByUsername, getUserRoles } from '../libraries/services';

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  tableId: {
    type: String,
    required: true,
  },
});

const queryClient = useQueryClient();

const username = ref();

const displayName = ref();

const password = ref<string>();

const edit = ref(false);

const title = computed(() => (edit.value ? '修改用户' : '新建用户'));

const roles = ref<string[]>();

const userRoles = ref<string[]>();

const initUserRoles = ref<string[]>();

const isPending = ref(false);

const closeDrawer = () => {
  emitter.emit('app:drawer', { close: true });
};

const initLocal = () => {
  edit.value = false;

  username.value = undefined;
  displayName.value = undefined;
  password.value = undefined;

  roles.value = undefined;
  userRoles.value = undefined;
  initUserRoles.value = undefined;
};

const initRoles = async (username?: string) => {
  roles.value = (await getRoles()).map(v => v.roleName);

  if (username) {
    userRoles.value = (await getUserRoles(username)).map(v => v.roleName);
    initUserRoles.value = userRoles.value.map(v => v);
  }
};

const initEdit = async (usernameToEdit: string) => {
  edit.value = true;

  const user = await getUserByUsername(usernameToEdit);
  if (!user) {
    message.error('用户不存在');
    closeDrawer();
    return;
  }

  username.value = user.username;
  displayName.value = user.displayName;
  password.value = undefined;
};

const handleOpen = async (usernameToEdit?: string) => {
  initLocal();

  await initRoles(usernameToEdit);

  if (!usernameToEdit) {
    return;
  }

  await initEdit(usernameToEdit);
};

const editUser = async () => {
  await api.post(ApiPaths.Admin.UPDATE_USER, {
    username: username.value,
    displayName: displayName.value,
    password: password.value,
  });

  const userRolesHasChange = userRoles.value?.sort().join() !== initUserRoles.value?.sort().join();
  if (userRolesHasChange) {
    await api.post(ApiPaths.Admin.UPDATE_USER_ROLES, {
      username: username.value,
      roles: userRoles.value ?? [],
    });
  }
};

const addUser = async () => {
  await api.post(ApiPaths.Admin.ADD_USER, {
    username: username.value,
    displayName: displayName.value,
    password: password.value,
    roles: userRoles.value ?? [],
  });
};

const handleSubmit = async () => {
  try {
    isPending.value = true;

    if (edit.value) {
      await editUser();
    } else {
      await addUser();
    }

    await queryClient.invalidateQueries({ queryKey: [props.tableId] });

    closeDrawer();

    message.success('保存成功');
  } finally {
    isPending.value = false;
  }
};
</script>
