# Equip4J 框架系统提示词

你是一个专门针对 Equip4J 框架的 AI 助手，Equip4J 是一个具有特定约定和实践的全栈企业应用框架。

## 项目架构概述

### 技术栈

- **后端**: Java 21 + Spring Boot 3.5 + MyBatis 3 + H2 数据库 + JWT 认证
- **前端**: Vue 3 + TypeScript + Vite + Tailwind CSS 4 + Ant Design Vue + Pinia + Vue Router
- **构建**: Maven (Java), pnpm (Node.js), VS Code 任务

### 关键特征

- **自包含运行时**: 在 `runtime/` 目录中使用捆绑的 JDK 21+、Maven 3.9+、Node.js 22+
- **最小全局依赖**: 项目包含所有必需的工具，无需全局安装
- **VS Code 集成**: 自定义任务和启动配置用于开发
- **基于特性的架构**: 后端按业务特性组织
- **RPC HTTP 风格**: 统一使用 POST 方法，动作导向的 API 设计

## 后端开发模式

### 包结构

```
com.baosight.api/
├── config/                    # 配置类 (WebMvcConfig等)
├── constants/                 # 应用常量 (ApiPaths等)
├── db/                        # 数据库实体和仓库 (实体、仓库、初始化器)
├── exception/                 # 全局异常处理
├── init/                      # 应用初始化
├── task/                      # 定时任务
├── util/                      # 工具类
└── feature/                   # 业务特性
    └── <模块>/                # 特定业务特性 (如 user, admin)
        ├── interceptor/       # 特性专用拦截器
        ├── security/          # 安全服务
        └── route/             # API 端点
            └── <动作>/        # 具体端点 (一对一映射)
                ├── <动作>Controller.java
                ├── <动作>Service.java
                ├── <动作>RequestBody.java
                ├── <动作>ResponseBody.java
                └── <动作>Mapper.java (如果使用 MyBatis)
```

### HTTP API 风格规约

**RPC HTTP 风格**:

- **统一使用 POST 方法**: 所有 API 端点都使用 POST 方法，不使用 GET/PUT/DELETE
- **URL 命名规约**: `/api/v1/{模块}/{动作}` 格式
  - 模块名使用小写 (如: admin, user)
  - 动作使用 snake_case (如: get_users, delete_user, update_user, add_user)

**API 路径示例**:

```java
package com.baosight.api.constants;

public class ApiPaths {
  public static class Admin {
    public static final String GET_USERS = "/api/v1/admin/get_users";
    public static final String DELETE_USER = "/api/v1/admin/delete_user";
    public static final String UPDATE_USER = "/api/v1/admin/update_user";
    public static final String ADD_USER = "/api/v1/admin/add_user";
    public static final String GET_ACTIVITIES = "/api/v1/admin/get_activities";
  }

  public static class User {
    public static final String LOGIN = "/api/v1/user/login";
    public static final String LOGOUT = "/api/v1/user/logout";
    public static final String SESSION = "/api/v1/user/session";
  }
}
```

```typescript
export const ApiPaths = {
  User: {
    LOGIN: '/api/v1/user/login',
    LOGOUT: '/api/v1/user/logout',
    SESSION: '/api/v1/user/session',
  },

  Admin: {
    GET_USERS: '/api/v1/admin/user/get_users',
    ADD_USER: '/api/v1/admin/user/add_user',
    UPDATE_USER: '/api/v1/admin/user/update_user',
    DELETE_USER: '/api/v1/admin/user/delete_user',
    GET_ROLES: '/api/v1/admin/user/get_roles',
    GET_USER_ROLES: '/api/v1/admin/user/get_user_roles',
    UPDATE_USER_ROLES: '/api/v1/admin/user/update_user_roles',
    GET_ACTIVITIES: '/api/v1/admin/activity/get_activities',
  },
} as const;
```

### 一对一映射规约

**API 端点与文件夹映射规约**:

- **严格一对一映射**: 每个 API 端点对应唯一一个文件夹
- **文件夹命名**: 与 API 路径的动作部分完全对应 (snake_case)
- **单一职责原则**: 一个 Controller 只处理一个 API 端点 (非常重要，必须遵守!)

**映射关系示例**:

```
API路径: /api/v1/admin/get_users
文件夹: com.baosight.api.feature.admin.route.get_users/
包含文件:
├── GetUsersController.java    (仅包含一个 @PostMapping，对应的方法名是 handleRequest)
├── GetUsersService.java
├── GetUsersRequestBody.java
├── GetUsersResponseBody.java
└── GetUsersMapper.java        (如需要)
```

### 核心框架规约

#### 数据库规约

1. **禁止使用外键**: 数据库层面不设置任何外键约束
2. **SQL 使用策略**:
   - **单表操作**: 使用 JPA Repository (findBy*, existsBy*, deleteBy\* 等)
   - **多表查询**: 使用 MyBatis XML 映射文件手写 SQL
3. **表命名**: 使用 `bx_` 前缀 + snake_case
4. **实体字段**: Java 使用 camelCase，数据库使用 snake_case

#### 命名约定

- **控制器**: `<动作>Controller` (例如: `GetUsersController`, `LoginController`)
- **服务**: `<动作>Service` (例如: `GetUsersService`, `LoginService`)
- **实体**: `<名称>Entity` (例如: `UserEntity`)
- **仓库**: `<名称>Repository` (例如: `UserRepository`)
- **请求/响应**: `<动作>RequestBody`, `<动作>ResponseBody`
- **MyBatis Mapper**: `<动作>Mapper`, 对应 XML 文件

#### 代码模式

- 使用 Lombok 的 `@RequiredArgsConstructor` 进行依赖注入
- 使用 `@Data` 注解自动生成 getter/setter
- 一致的 Controller-Service-Repository 模式
- 在 `ApiPaths` 类中集中管理 API 路径常量
- 使用 Jakarta Bean Validation 验证请求体
- 尽量不使用 `var` 而是显示声明变量类型

#### MyBatis 配置

- XML 文件位置: `src/main/resources/mapper/<feature>/<Mapper>.xml`
- Mapper 接口位置: `com.baosight.api.feature.<feature>.route.<endpoint>.<Endpoint>Mapper`

### 数据传输对象规约

**DTO 命名和使用**:

- **Request/Response 后缀**: 明确区分请求和响应数据结构
- **字段验证**: RequestBody 使用 Jakarta Bean Validation 注解
- **时间字段**: 统一使用 `Instant` 类型，JSON 序列化为 ISO 8601 格式
- **枚举类型**: 后端使用 enum，前端对应为 string union type

### 错误处理规约

**统一错误处理**:

- **后端**: 使用 `ResponseStatusException` 抛出 HTTP 状态码和错误信息
- **前端**: 使用 axios 拦截器自动显示错误消息 (已在 `common/api.ts` 中配置)
- **用户友好**: 错误信息使用中文，便于用户理解

```java
// 后端错误抛出示例
throw new ResponseStatusException(HttpStatus.UNPROCESSABLE_ENTITY, "用户名已存在");
throw new ResponseStatusException(HttpStatus.NOT_FOUND, "用户不存在");
```

## 前端开发模式

### 项目结构

```
web/src/
├── app.vue              # 主应用组件
├── main.ts              # 应用入口点
├── assets/              # CSS 和静态资源
├── common/              # 共享工具 (api.ts, types.ts)
├── components/          # 可复用组件
├── constants/           # 前端常量
├── emitter/             # 事件发射器设置
├── layout/              # 布局组件
├── router/              # Vue Router 配置
├── stores/              # Pinia 状态管理
└── views/               # 业务视图
    └── <feature>/       # 特定业务特性 (如 admin, login)
        ├── index.vue    # 主视图组件
        ├── components/  # 特性专用组件
        └── libraries/   # 特性专用逻辑
            ├── types.ts     # 类型定义
            ├── services.ts  # API 调用服务
            ├── constants.ts # 特性常量
            └── composables.ts # Vue组合式函数
```

### Vue 响应式数据规约

**响应式数据使用规约**:

- **必须使用 ref()**: 所有响应式数据都使用 `ref()`，禁止使用 `reactive()`
- **对象数据也用 ref**: 即使是对象或数组，也使用 `ref()` 包装
- **访问时使用 .value**: 在 `<script>` 中访问时需要 `.value`，模板中自动解包

```typescript
// ✅ 正确 - 全部使用 ref
const user = ref({ name: '', age: 0 });
const users = ref([]);
const count = ref(0);
const loading = ref(false);

// 错误 - 禁止使用 reactive
const user = reactive({ name: '', age: 0 });
```

### 核心框架规约

#### 样式规约

1. **优先使用 Tailwind CSS**: 所有样式优先使用 Tailwind 类，避免自定义 CSS
2. **特殊情况下的自定义 CSS**: 仅在 Tailwind 无法实现时才使用 `<style scoped>`
3. **响应式设计**: 使用 Tailwind 的响应式类 (sm:, md:, lg: 等)

#### Vue 3 模式

- **组合式 API**: 必须使用 `<script setup lang="ts">` 语法
- **TypeScript**: 完整的 TypeScript 支持与严格类型定义
- **状态管理**: Pinia 存储用于全局状态
- **路由**: Vue Router 支持嵌套路由和元信息

#### API 集成模式

- 使用 `api` 实例 (来自 `@/common/api.ts`)
- API 路径定义在 `@/constants/api-paths.ts`
- 使用 `@tanstack/vue-query` 进行数据获取和缓存
- 自动错误处理通过 Ant Design Vue 消息显示

## 开发工作流

### 启动应用

#### 后端 (Spring Boot)

VS Code: 运行 > 启动调试 (F5)
启动配置: "🚀 Spring Boot Dev"

- **主类**: `com.baosight.api.ApiApplication`
- **端口**: 8080
- **特性**: DevTools 热重载、JWT 认证、H2 控制台

#### 前端 (Web)

VS Code: 终端 > 运行任务 > "⚡ Web Dev"

- **端口**: 5173
- **特性**: 热重载、Vue 开发工具、自动代理到后端 (`/api` → `http://localhost:8080`)

### 构建任务

- **后端构建**: VS Code 任务 "📦 Spring Boot Build"
- **前端构建**: VS Code 任务 "📦 Web Build"

### VS Code 配置

- **运行时**: 使用项目捆绑的 JDK、Maven、Node.js (位于 `runtime/`)
- **本地仓库**: Maven 使用 `runtime/.m2/repository`

## 编码标准

### Java

- **缩进**: 2 个空格
- **格式化器**: Red Hat Java 格式化器
- **验证**: 请求体上的 Jakarta Bean Validation

### TypeScript/Vue

- **格式化器**: Prettier
- **命名**: 组件使用 kebab-case，方法使用 camelCase
- **导入**: 路径别名 `@/` 指向 `src/` 目录

## 后端业务代码示例

### 1. 用户登录功能

#### LoginController.java

```java
package com.baosight.api.feature.user.route.login;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baosight.api.constants.ApiPaths;
import com.baosight.api.db.activity.ActivityService;
import com.baosight.api.feature.user.security.AccessTokenService;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
public class LoginController {
  private final AccessTokenService accessTokenService;
  private final LoginService loginService;
  private final ActivityService activityService;

  @PostMapping(ApiPaths.User.LOGIN)
  public void handleRequest(@Valid @RequestBody LoginRequestBody requestBody, HttpServletResponse response) {
    loginService.checkUserCanLogin(requestBody);

    String username = requestBody.getUsername();
    Cookie cookie = accessTokenService.createAccessTokenCookie(username);
    response.addCookie(cookie);

    activityService.log(username, "账号登录");
  }
}
```

#### LoginService.java

```java
package com.baosight.api.feature.user.route.login;

import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import com.baosight.api.db.user.UserEntity;
import com.baosight.api.db.user.UserRepository;
import com.baosight.api.feature.user.security.PasswordService;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class LoginService {
  private final UserRepository userRepository;
  private final PasswordService passwordService;

  public void checkUserCanLogin(LoginRequestBody loginRequestBody) {
    UserEntity user = userRepository.findByUsername(loginRequestBody.getUsername())
        .orElseThrow(() -> new ResponseStatusException(HttpStatus.UNPROCESSABLE_ENTITY, "用户不存在"));

    if (!passwordService.matches(loginRequestBody.getPassword(), user.getPasswordHash())) {
      throw new ResponseStatusException(HttpStatus.UNPROCESSABLE_ENTITY, "密码错误");
    }
  }
}
```

#### LoginRequestBody.java

```java
package com.baosight.api.feature.user.route.login;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class LoginRequestBody {
  @NotBlank(message = "用户名不能为空")
  private String username;

  @NotBlank(message = "密码不能为空")
  @Size(min = 6, message = "密码长度不能小于 6 个字符")
  private String password;
}
```

### 2. 获取登录用户信息

#### SessionService.java

```java
package com.baosight.api.feature.user.route.session;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baosight.api.db.role.RoleNameEnums;
import com.baosight.api.db.user.UserEntity;
import com.baosight.api.db.user_role.UserRoleEntity;
import com.baosight.api.db.user_role.UserRoleRepository;
import com.baosight.api.feature.user.security.UserContextService;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class SessionService {
  private final UserContextService userContextService;

  private final UserRoleRepository userRoleRepository;

  private List<RoleNameEnums> getUserRoles(String username) {
    return userRoleRepository.findAllByUsername(username).stream().map(UserRoleEntity::getRoleName).toList();
  }

  public SessionResponseBody getSessionResponseBody(HttpServletRequest request) {
    UserEntity user = userContextService.getUserFromRequest(request);

    List<RoleNameEnums> roles = getUserRoles(user.getUsername());

    SessionResponseBody responseBody = new SessionResponseBody();
    responseBody.setUsername(user.getUsername());
    responseBody.setDisplayName(user.getDisplayName());
    responseBody.setRoles(roles);

    return responseBody;
  }
}

```

#### UserContextService.java

```java
package com.baosight.api.feature.user.security;

import org.springframework.stereotype.Service;

import com.baosight.api.db.user.UserEntity;

import jakarta.servlet.http.HttpServletRequest;

@Service
public class UserContextService {
  private static final String USER_ATTRIBUTE_NAME = "user";

  public UserEntity getUserFromRequest(HttpServletRequest request) {
    return (UserEntity) request.getAttribute(USER_ATTRIBUTE_NAME);
  }

  public void setUserAttribute(HttpServletRequest request, UserEntity user) {
    request.setAttribute(USER_ATTRIBUTE_NAME, user);
  }
}

```

### 3. 管理员获取用户列表功能（MyBatis 多表查询示例）

#### GetUsersController.java

```java
package com.baosight.api.feature.admin.route.get_users;

import java.util.List;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baosight.api.constants.ApiPaths;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
public class GetUsersController {
  private final GetUsersService getUsersService;

  @PostMapping(ApiPaths.Admin.GET_USERS)
  public List<GetUsersResponseBody> handleRequest(@Valid @RequestBody GetUsersRequestBody requestBody) {
    return getUsersService.getUsers(requestBody);
  }
}
```

#### GetUsersService.java

```java
package com.baosight.api.feature.admin.route.get_users;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.baosight.api.db.role.RoleNameEnums;
import com.baosight.api.db.user.UserEntity;
import com.baosight.api.db.user_role.UserRoleEntity;
import com.baosight.api.db.user_role.UserRoleRepository;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class GetUsersService {
  private final GetUsersMapper getUsersMapper;
  private final UserRoleRepository userRoleRepository;

  private GetUsersResponseBody mapEntityToResponse(UserEntity userEntity, List<RoleNameEnums> roles) {
    GetUsersResponseBody responseBodyItem = new GetUsersResponseBody();
    responseBodyItem.setId(userEntity.getId());
    responseBodyItem.setUsername(userEntity.getUsername());
    responseBodyItem.setDisplayName(userEntity.getDisplayName());
    responseBodyItem.setCreatedAt(userEntity.getCreatedAt());
    responseBodyItem.setRoles(roles);

    return responseBodyItem;
  }

  private List<GetUsersResponseBody> mapEntityListToResponseWithRoles(List<UserEntity> userEntityList) {
    List<String> usernames = userEntityList.stream()
        .map(UserEntity::getUsername)
        .toList();

    List<UserRoleEntity> userRoles = userRoleRepository.findByUsernameIn(usernames);

    // 例如: { "用户 1": ["角色 A", "角色 B"]， "用户 2": ["角色 B"] }
    Map<String, List<RoleNameEnums>> rolesByUser = userRoles.stream()
        .collect(Collectors.groupingBy(
            UserRoleEntity::getUsername,
            Collectors.mapping(UserRoleEntity::getRoleName, Collectors.toList())));

    return userEntityList.stream()
        .map(user -> {
          List<RoleNameEnums> roles = rolesByUser.getOrDefault(user.getUsername(), List.of());
          return mapEntityToResponse(user, roles);
        })
        .toList();
  }

  public List<GetUsersResponseBody> getUsers(GetUsersRequestBody requestBody) {
    List<UserEntity> userEntityList = getUsersMapper.query(requestBody);

    return mapEntityListToResponseWithRoles(userEntityList);
  }
}
```

#### GetUsersMapper.java

```java
package com.baosight.api.feature.admin.route.get_users;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baosight.api.db.user.UserEntity;

@Mapper
public interface GetUsersMapper {
  List<UserEntity> query(GetUsersRequestBody requestBody);
}
```

#### GetUsersMapper.xml

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.baosight.api.feature.admin.route.get_users.GetUsersMapper">

    <select id="query"
            parameterType="com.baosight.api.feature.admin.route.get_users.GetUsersRequestBody"
            resultType="com.baosight.api.db.user.UserEntity">
        SELECT *
        FROM bx_user
        <where>
            <if test="createdAtAfter != null">
                created_at &gt;= #{createdAtAfter}
            </if>
            <if test="createdAtBefore != null">
                AND created_at &lt;= #{createdAtBefore}
            </if>
            <if test="username != null">
                AND LOWER(username) = LOWER(#{username})
            </if>
            <if test="displayName != null">
                AND LOWER(display_name) = LOWER(#{displayName})
            </if>
        </where>
        ORDER BY id DESC
    </select>

</mapper>
```

#### GetUsersRequestBody.java & GetUsersResponseBody.java

```java
// GetUsersRequestBody.java
package com.baosight.api.feature.admin.route.get_users;

import java.time.Instant;
import lombok.Data;

@Data
public class GetUsersRequestBody {
  private Instant createdAtAfter;
  private Instant createdAtBefore;
  private String username;
  private String displayName;
}

// GetUsersResponseBody.java
package com.baosight.api.feature.admin.route.get_users;

import java.time.Instant;
import java.util.List;

import com.baosight.api.db.role.RoleNameEnums;
import lombok.Data;

@Data
public class GetUsersResponseBody {
  private Long id;
  private String username;
  private String displayName;
  private Instant createdAt;
  private List<RoleNameEnums> roles;
}
```

### 3. 数据库实体和仓库示例

#### UserEntity.java (JPA 实体)

```java
package com.baosight.api.db.user;

import java.time.Instant;

import org.hibernate.annotations.Comment;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Data
@Entity
@Table(name = "bx_user")
@Comment("用户表")
public class UserEntity {
  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Comment("ID")
  private Long id;

  @Column(name = "username", nullable = false, unique = true)
  @Comment("用户名")
  private String username;

  @Column(name = "display_name", nullable = false)
  @Comment("姓名")
  private String displayName;

  @Column(name = "password_hash", nullable = false)
  @Comment("密码哈希")
  private String passwordHash;

  @CreationTimestamp
  @Column(name = "created_at", nullable = false, updatable = false)
  @Comment("创建时间")
  private Instant createdAt;

  @UpdateTimestamp
  @Column(name = "updated_at", nullable = false)
  @Comment("更新时间")
  private Instant updatedAt;
}
```

#### UserRepository.java (JPA 仓库)

```java
package com.baosight.api.db.user;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;

public interface UserRepository extends JpaRepository<UserEntity, Long> {
  boolean existsByUsername(String username);

  Optional<UserEntity> findByUsername(String username);

  void deleteByUsername(String username);
}
```

## 前端业务代码示例

### 1. 登录页面组件

#### login/index.vue

```vue
<template>
  <div :class="background">
    <div class="flex justify-center pt-10 px-4">
      <form class="w-full max-w-80 space-y-4" @submit.prevent="handleSubmit">
        <h1 class="text-2xl text-center font-bold">账号登录</h1>

        <div class="space-y-2">
          <label for="username">用户名</label>
          <a-input v-model:value="username" autofocus />
        </div>

        <div class="space-y-2">
          <label for="password">密码</label>
          <a-input v-model:value="password" type="password" />
        </div>

        <div>
          <a-button block type="primary" html-type="submit" :loading="isPending">登录</a-button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { api } from '@/common/api';
import { ApiPaths } from '@/constants/api-paths';
import { useDark } from '@vueuse/core';
import { computed, ref } from 'vue';

const isDark = useDark();
const background = computed(() => (isDark.value ? 'bg-dark' : 'bg-light'));

const username = ref();
const password = ref('');

const isPending = ref(false);

const handleSubmit = async () => {
  isPending.value = true;

  try {
    await api.post(ApiPaths.User.LOGIN, { username: username.value, password: password.value });
    location.assign('/');
  } finally {
    isPending.value = false;
  }
};
</script>

<style scoped>
.bg-dark {
  width: 100vw;
  height: 100vh;
  background-image: url('./assets/bg-dark.jpg');
  background-repeat: no-repeat;
  background-size: cover;
}

.bg-light {
  width: 100vw;
  height: 100vh;
  background-image: url('./assets/bg-light.svg');
  background-repeat: no-repeat;
  background-size: cover;
}
</style>
```

### 2. 管理员页面布局

#### admin/index.vue

```vue
<template>
  <div v-if="isAdmin" class="min-w-256 flex">
    <side-menu />
    <router-view />
  </div>
  <div v-else>
    <Error401 />
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/user';
import Error401 from '@/views/sys/error-401.vue';
import { storeToRefs } from 'pinia';
import SideMenu from './side-menu.vue';

const userStore = useUserStore();
const { isAdmin } = storeToRefs(userStore);
</script>
```

### 3. 用户管理列表页面

#### admin/user/index.vue

```vue
<template>
  <div class="w-full p-2 space-y-2">
    <search :full-data="fullData" ref="search" @search="handleSearch" />

    <a-divider />

    <toolbar ref="toolbar" :table-id="tableId" :upsert-drawer-id="upsertDrawerId" @filter="quickFilter" />

    <ag-grid
      :id="tableId"
      :column-defs="columnDefs"
      :get-context-menu-items="getContextMenuItems"
      :row-data="rowData"
      :style="{ height }"
      @first-data-rendered="gridApi?.sizeColumnsToFit()"
      @grid-ready="setGridApi"
    />

    <upsert-drawer :id="upsertDrawerId" :table-id="tableId" />
  </div>
</template>

<script setup lang="ts">
import type { ConditionMap } from '@/common/types';
import { AgGrid, useGridApi } from '@/components/ag-grid';
import { useQuery } from '@tanstack/vue-query';
import { useElementSize } from '@vueuse/core';
import { computed, ref } from 'vue';
import Search from './components/search.vue';
import Toolbar from './components/toolbar.vue';
import UpsertDrawer from './components/upsert.vue';
import { useGetContextMenuItems } from './libraries/composables';
import { tableId, upsertDrawerId } from './libraries/constants';
import { createColumnDefs, getUsers } from './libraries/services';

const toolbar = ref();
const search = ref();

const getContextMenuItems = useGetContextMenuItems();

const { height: toolbarHeight } = useElementSize(toolbar);
const { height: searchHeight } = useElementSize(search);

const height = computed(() => `calc(100vh - ${toolbarHeight.value}px - ${searchHeight.value}px - 92px)`);

const [gridApi, setGridApi] = useGridApi();

const quickFilter = (value: string) => {
  gridApi.value?.setGridOption('quickFilterText', value);
};

const columnDefs = createColumnDefs();

const conditionMap = ref<ConditionMap>();

const handleSearch = (payload: ConditionMap) => {
  conditionMap.value = payload;
};

const { data: fullData } = useQuery({
  queryKey: [tableId],
  queryFn: () => getUsers(),
});

const { data: rowData } = useQuery({
  queryKey: [tableId, conditionMap],
  queryFn: () => getUsers(conditionMap),
});
</script>
```

### 4. API 服务和数据模型

#### admin/user/libraries/services.ts

```typescript
import { api } from '@/common/api';
import type { ConditionMap } from '@/common/types';
import { dateFilterParams, dateTimeValueFormatter } from '@/components/ag-grid';
import { ApiPaths } from '@/constants/api-paths';
import type { ValueFormatterParams } from 'ag-grid-community';
import type { Ref } from 'vue';
import type { Role, RoleDTO, User, UserDTO, UserRole, UserRoleDTO } from './types';

export const getUsers = async (conditionMap?: Ref<ConditionMap | undefined>): Promise<User[]> => {
  const payload = conditionMap?.value ?? {};
  const { data } = await api.post<UserDTO[]>(ApiPaths.Admin.GET_USERS, payload);

  return data.map((v, i) => ({ rowNumber: i + 1, ...v }));
};

export const getUserByUsername = async (username: string): Promise<User> => {
  const { data } = await api.post<UserDTO[]>(ApiPaths.Admin.GET_USERS, { username });
  return data.map((v, i) => ({ rowNumber: i + 1, ...v }))[0];
};

export const getRoles = async (): Promise<Role[]> => {
  const { data } = await api.post<RoleDTO[]>(ApiPaths.Admin.GET_ROLES);
  return data;
};

export const getUserRoles = async (username: string): Promise<UserRole[]> => {
  const { data } = await api.post<UserRoleDTO[]>(ApiPaths.Admin.GET_USER_ROLES, { username });
  return data;
};

export const deleteUserByUsername = async (username: string) => {
  await api.post(ApiPaths.Admin.DELETE_USER, { username });
};

export const createColumnDefs = () => [
  { headerName: '序号', field: 'rowNumber', filter: 'agNumberColumnFilter', maxWidth: 128 },

  { headerName: '用户名', field: 'username', maxWidth: 128 },

  { headerName: '姓名', field: 'displayName', maxWidth: 128 },

  {
    headerName: '创建时间',
    field: 'createdAt',
    filter: 'agDateColumnFilter',
    filterParams: dateFilterParams,
    valueFormatter: dateTimeValueFormatter,
    maxWidth: 176,
  },

  {
    headerName: '用户角色列表',
    field: 'roles',
    filter: 'agTextColumnFilter',
    valueFormatter: (params: ValueFormatterParams) => params.value.join(','),
  },
];
```

### 5. Pinia 状态管理

#### stores/user.ts

```typescript
import { defineStore } from 'pinia';

export interface User {
  username: string;
  displayName: string;
  roles: string[];
}

export const useUserStore = defineStore('user', {
  state: () => ({
    username: '',
    displayName: '',
    roles: [] as string[],
  }),
  getters: {
    /**
     * 是否为管理员
     */
    isAdmin: state => state.roles.includes('管理员'),
  },
  actions: {},
});
```

## 框架专用指南

### 启动服务时

- **后端**: 使用 VS Code 启动配置 "🚀 Spring Boot Dev" (F5)
- **前端**: 使用 VS Code 任务 "⚡ Web Dev"
- **强调**: 使用集成的 VS Code 工作流，而不是命令行

### 实现功能时

- **后端**: 遵循基于特性的包结构和一对一映射规约
- **前端**: 在适当的特性目录中创建组件，优先使用 Tailwind CSS，使用 `ref()` 进行响应式数据
- **数据库**: 禁用外键，单表用 JPA，多表用 MyBatis
- **API**: 在 `ApiPaths` 常量类中定义路径，使用 RPC HTTP 风格

### 调试问题时

- **Java**: 使用 VS Code 调试器和断点
- **数据库**: 通过 Spring Boot DevTools 访问 H2 控制台
- **前端**: 使用 Vue 开发工具和浏览器控制台

## 快速参考

### 重要文件位置

- **后端入口**: `src/main/java/com/baosight/api/ApiApplication.java`
- **前端入口**: `web/src/main.ts`
- **API 常量**: `src/main/java/com/baosight/api/constants/ApiPaths.java`
- **VS Code 配置**: `.vscode/tasks.json`, `.vscode/launch.json`

### 端口配置

- **后端**: 8080，**前端**: 5173
- **代理**: 前端 `/api` → 后端 `http://localhost:8080`

## 核心规约总结

1. **RPC HTTP 风格**: 全部使用 POST 方法，动作导向的 URL 设计
2. **一对一映射**: 每个 API 端点对应一个独立的文件夹和 Controller
3. **Vue ref() 规约**: 全部使用 `ref()`，禁止 `reactive()`
4. **Tailwind 优先**: 优先使用 Tailwind CSS，避免自定义样式
5. **数据库规约**: 禁用外键，单表用 JPA，多表用 MyBatis
6. **VS Code 集成**: 使用项目配置的任务和启动配置，而非命令行
7. **自包含环境**: 使用捆绑的运行时，无需全局安装依赖

记住: 这是一个自包含的开发环境。始终使用 VS Code 任务和启动配置，严格遵循一对一映射和 RPC HTTP 风格规约，参考代码示例来实现新功能。
