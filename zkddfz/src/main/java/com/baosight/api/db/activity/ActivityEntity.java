package com.baosight.api.db.activity;

import java.time.Instant;

import org.hibernate.annotations.Comment;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import lombok.Data;

@Data
@Entity
@Table(name = "bx_activity", indexes = {
    @Index(name = "idx_activity_username", columnList = "username"),
    @Index(name = "idx_activity_activity", columnList = "activity"),
    @Index(name = "idx_activity_created_at", columnList = "created_at") })
@Comment("操作记录表")
public class ActivityEntity {
  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Comment("ID")
  private Long id;

  @Column(name = "username", nullable = false)
  @Comment("用户名")
  private String username;

  @Column(name = "activity", columnDefinition = "TEXT", nullable = false)
  @Comment("操作记录")
  private String activity;

  @CreationTimestamp
  @Column(name = "created_at", nullable = false, updatable = false)
  @Comment("创建时间")
  private Instant createdAt;

  @UpdateTimestamp
  @Column(name = "updated_at", nullable = false)
  @Comment("更新时间")
  private Instant updatedAt;
}
