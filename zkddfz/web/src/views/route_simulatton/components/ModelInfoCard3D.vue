<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue';
import * as THREE from 'three';

interface ModelData {
  id: string;
  name: string;
  currentPosition?: { x: number; y: number; z: number };
  currentRotation?: { x: number; y: number; z: number };
  battery?: number;
  consume?: number; // 累计耗电量
  distance?: number; // 走行累计距离
  type?: string;
  path?: string;
}

interface Props {
  model: ModelData;
  scene?: THREE.Scene;
  camera?: THREE.Camera;
  visible?: boolean;
  modelMesh?: THREE.Object3D; // 实际的3D模型对象
}

const props = defineProps<Props>();

// 3D卡片对象
let cardGroup: THREE.Group | null = null;
let cardMesh: THREE.Mesh | null = null;
let textSprite: THREE.Sprite | null = null;

// 设备类型映射
const deviceTypeMap = {
  '/models/new/桥式转载机ok.glb': '桥式转载机',
  '/models/new/大型布料机ok-6.glb': '大型布料机',
  '/models/new/中继转载机-B-ok.glb': '中继转载机',
  '/models/new/扇形布料机ok.glb': '扇形布料机',
  '/models/new/3500T斗轮挖掘机.glb': '3500T斗轮挖掘机',
  '/models/new/1500T斗轮挖掘机.glb': '1500T斗轮挖掘机',
  '/models/new/移动转载机ok.glb': '移动转载机',
};

// 获取设备类型
const getDeviceType = () => {
  if (props.model.path && deviceTypeMap[props.model.path]) {
    return deviceTypeMap[props.model.path];
  }
  return props.model.type || '未知设备';
};

// 格式化坐标
const formatCoordinate = (value: number) => {
  return value?.toFixed(1) || '0.0';
};

// 格式化角度
const formatAngle = (value: number) => {
  return value?.toFixed(0) || '0';
};

// 提取车辆编号
const getVehicleNumber = () => {
  const id = props.model.id;
  const match = id.match(/\d+/);
  return match ? match[0] : id.slice(-3);
};

// 绘制电池样式
const drawBattery = (context: CanvasRenderingContext2D, x: number, y: number, width: number, height: number, percentage: number) => {
  const batteryWidth = width;
  const batteryHeight = height;
  const capWidth = 4;
  const capHeight = height * 0.4;

  // 电池外框
  context.strokeStyle = '#ffffff';
  context.lineWidth = 2;
  context.strokeRect(x, y, batteryWidth, batteryHeight);

  // 电池正极帽
  context.fillStyle = '#ffffff';
  context.fillRect(x + batteryWidth, y + (batteryHeight - capHeight) / 2, capWidth, capHeight);

  // 电池内部背景
  context.fillStyle = 'rgba(50, 50, 50, 0.8)';
  context.fillRect(x + 2, y + 2, batteryWidth - 4, batteryHeight - 4);

  // 电量填充
  const fillWidth = (batteryWidth - 4) * (percentage / 100);
  let fillColor;

  if (percentage <= 20) {
    fillColor = '#ff4444'; // 红色 - 低电量
  } else if (percentage <= 50) {
    fillColor = '#ffaa00'; // 橙色 - 中等电量
  } else {
    fillColor = '#44ff44'; // 绿色 - 高电量
  }

  // 电量渐变效果
  const gradient = context.createLinearGradient(x + 2, y + 2, x + 2 + fillWidth, y + 2);
  gradient.addColorStop(0, fillColor);
  gradient.addColorStop(0.5, fillColor + 'CC'); // 半透明
  gradient.addColorStop(1, fillColor);

  context.fillStyle = gradient;
  context.fillRect(x + 2, y + 2, fillWidth, batteryHeight - 4);

  // 电量发光效果
  if (percentage > 0) {
    context.shadowColor = fillColor;
    context.shadowBlur = 8;
    context.fillRect(x + 2, y + 2, fillWidth, batteryHeight - 4);
    context.shadowBlur = 0;
  }

  // 电量分段指示器
  context.strokeStyle = 'rgba(255, 255, 255, 0.3)';
  context.lineWidth = 1;
  for (let i = 1; i < 4; i++) {
    const segmentX = x + (batteryWidth - 4) * (i / 4) + 2;
    context.beginPath();
    context.moveTo(segmentX, y + 2);
    context.lineTo(segmentX, y + batteryHeight - 2);
    context.stroke();
  }

  // 电量百分比文字
  context.fillStyle = '#ffffff';
  context.font = 'bold 14px Arial';
  context.textAlign = 'center';
  context.textBaseline = 'middle';
  context.shadowColor = '#000000';
  context.shadowBlur = 2;
  context.fillText(`${percentage}%`, x + batteryWidth / 2, y + batteryHeight / 2);
  context.shadowBlur = 0;
};

// 绘制大型电池样式（用于突出显示）
// const drawLargeBattery = (context: CanvasRenderingContext2D, x: number, y: number, width: number, height: number, percentage: number) => {
//   const batteryWidth = width;
//   const batteryHeight = height;
//   const capWidth = 8;
//   const capHeight = height * 0.6;

//   // 电池外框 - 更粗的边框
//   context.strokeStyle = '#ffffff';
//   context.lineWidth = 3;
//   context.strokeRect(x, y, batteryWidth, batteryHeight);

//   // 电池正极帽
//   context.fillStyle = '#ffffff';
//   context.fillRect(x + batteryWidth, y + (batteryHeight - capHeight) / 2, capWidth, capHeight);

//   // 电池内部背景
//   context.fillStyle = 'rgba(20, 20, 20, 0.9)';
//   context.fillRect(x + 3, y + 3, batteryWidth - 6, batteryHeight - 6);

//   // 电量填充
//   const fillWidth = (batteryWidth - 6) * (percentage / 100);
//   let fillColor;

//   if (percentage <= 20) {
//     fillColor = '#ff4444'; // 红色 - 低电量
//   } else if (percentage <= 50) {
//     fillColor = '#ffaa00'; // 橙色 - 中等电量
//   } else {
//     fillColor = '#44ff44'; // 绿色 - 高电量
//   }

//   // 电量渐变效果 - 更强烈的渐变
//   const gradient = context.createLinearGradient(x + 3, y + 3, x + 3 + fillWidth, y + 3);
//   gradient.addColorStop(0, fillColor);
//   gradient.addColorStop(0.3, fillColor + 'DD');
//   gradient.addColorStop(0.7, fillColor + 'AA');
//   gradient.addColorStop(1, fillColor);

//   context.fillStyle = gradient;
//   context.fillRect(x + 3, y + 3, fillWidth, batteryHeight - 6);

//   // 电量发光效果 - 更强的发光
//   if (percentage > 0) {
//     context.shadowColor = fillColor;
//     context.shadowBlur = 15;
//     context.fillRect(x + 3, y + 3, fillWidth, batteryHeight - 6);
//     context.shadowBlur = 0;
//   }

//   // 电量分段指示器 - 更多分段
//   context.strokeStyle = 'rgba(255, 255, 255, 0.4)';
//   context.lineWidth = 2;
//   for (let i = 1; i < 5; i++) {
//     const segmentX = x + (batteryWidth - 6) * (i / 5) + 3;
//     context.beginPath();
//     context.moveTo(segmentX, y + 3);
//     context.lineTo(segmentX, y + batteryHeight - 3);
//     context.stroke();
//   }

//   // 电池边框发光效果
//   context.strokeStyle = fillColor;
//   context.lineWidth = 1;
//   context.shadowColor = fillColor;
//   context.shadowBlur = 8;
//   context.strokeRect(x, y, batteryWidth, batteryHeight);
//   context.shadowBlur = 0;
// };

// 创建文本纹理
const createTextTexture = (text: string, options = {}) => {
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');

  if (!context) {
    console.error('无法获取canvas context');
    return null;
  }

  const {
    fontSize = 20,
    fontFamily = 'Arial, Microsoft YaHei, sans-serif',
    color = '#ffffff',
    backgroundColor = 'rgba(0, 20, 40, 0.95)', // 深蓝色背景
    padding = 12,
    borderColor = '#00ff88', // 亮绿色边框
    borderWidth = 3,
    glowColor = '#00ff88' // 发光颜色
  } = options;

  // 设置画布尺寸 - 更大更高的卡片
  canvas.width = 600;
  canvas.height = 450; // 提升一半高度

  // 绘制渐变背景
  const gradient = context.createLinearGradient(0, 0, canvas.width, canvas.height);
  gradient.addColorStop(0, 'rgba(0, 30, 60, 0.98)'); // 深蓝色
  gradient.addColorStop(0.5, 'rgba(0, 20, 40, 0.95)'); // 中间色
  gradient.addColorStop(1, 'rgba(0, 10, 30, 0.98)'); // 更深蓝色

  context.fillStyle = gradient;
  context.fillRect(0, 0, canvas.width, canvas.height);

  // 绘制发光边框效果
  context.shadowColor = glowColor;
  context.shadowBlur = 15;
  context.strokeStyle = borderColor;
  context.lineWidth = borderWidth;
  context.strokeRect(borderWidth/2, borderWidth/2, canvas.width - borderWidth, canvas.height - borderWidth);

  // 绘制内边框（双边框效果）
  context.shadowBlur = 0;
  context.strokeStyle = 'rgba(0, 255, 136, 0.6)';
  context.lineWidth = 1;
  context.strokeRect(borderWidth + 2, borderWidth + 2, canvas.width - (borderWidth + 2) * 2, canvas.height - (borderWidth + 2) * 2);

  // 绘制科技感装饰线条
  context.strokeStyle = 'rgba(0, 255, 136, 0.4)';
  context.lineWidth = 1;

  // 左上角装饰
  context.beginPath();
  context.moveTo(padding, padding + 20);
  context.lineTo(padding, padding);
  context.lineTo(padding + 20, padding);
  context.stroke();

  // 右下角装饰
  context.beginPath();
  context.moveTo(canvas.width - padding - 20, canvas.height - padding);
  context.lineTo(canvas.width - padding, canvas.height - padding);
  context.lineTo(canvas.width - padding, canvas.height - padding - 20);
  context.stroke();

  // 解析文本内容
  const lines = text.split('\n');
  const batteryLine = lines.find(line => line.includes('电量:'));
  const batteryMatch = batteryLine ? batteryLine.match(/(\d+)%/) : null;
  const batteryPercentage = batteryMatch ? parseInt(batteryMatch[1]) : 100;

  // 左右分布布局
  const leftColumnX = padding + 10;
  const rightColumnX = canvas.width / 2 + 20;
  const columnWidth = canvas.width / 2 - 40;

  // 绘制标题（横跨整个宽度）
  const titleLine = lines[0];
  context.fillStyle = '#00ff88';
  context.font = `bold ${fontSize + 8}px ${fontFamily}`;
  context.shadowColor = '#00ff88';
  context.shadowBlur = 12;
  context.textAlign = 'center';
  context.textBaseline = 'top';
  context.fillText(titleLine, canvas.width / 2, padding + 10);
  context.shadowBlur = 0;

  // 绘制分隔线
  context.strokeStyle = 'rgba(0, 255, 136, 0.5)';
  context.lineWidth = 2;
  context.beginPath();
  context.moveTo(padding + 20, padding + 50);
  context.lineTo(canvas.width - padding - 20, padding + 50);
  context.stroke();

  // 左侧信息区域
  let leftY = padding + 70;
  const leftLineHeight = fontSize + 6;

  // 绘制左侧信息
  const leftInfo = lines.slice(1).filter(line => !line.includes('电量:'));
  leftInfo.forEach((line, index) => {
    if (leftY < canvas.height - padding - 40) {
      if (line.includes('类型:')) {
        context.fillStyle = '#00d4ff';
        context.font = `bold ${fontSize + 2}px ${fontFamily}`;
        context.shadowColor = '#00d4ff';
        context.shadowBlur = 6;
      } else if (line.includes('坐标:')) {
        context.fillStyle = '#ffff00';
        context.font = `${fontSize}px ${fontFamily}`;
        context.shadowColor = '#ffff00';
        context.shadowBlur = 4;
      } else if (line.includes('角度:')) {
        context.fillStyle = '#ff8800';
        context.font = `${fontSize}px ${fontFamily}`;
        context.shadowColor = '#ff8800';
        context.shadowBlur = 4;
      } else if (line.includes('状态:')) {
        context.fillStyle = '#44ff44';
        context.font = `bold ${fontSize}px ${fontFamily}`;
        context.shadowColor = '#44ff44';
        context.shadowBlur = 4;
      } else {
        context.fillStyle = '#ffffff';
        context.font = `${fontSize}px ${fontFamily}`;
        context.shadowColor = '#ffffff';
        context.shadowBlur = 3;
      }

      context.textAlign = 'left';
      context.textBaseline = 'top';
      context.fillText(line, leftColumnX, leftY);
      context.shadowBlur = 0;

      leftY += leftLineHeight;
    }
  });

  // 右侧电量区域 - 突出显示
  const batteryAreaY = padding + 70;
  const batteryAreaHeight = 200;

  // 绘制电量区域背景
  // const batteryBgGradient = context.createRadialGradient(
  //   rightColumnX + columnWidth / 2, batteryAreaY + batteryAreaHeight / 2, 0,
  //   rightColumnX + columnWidth / 2, batteryAreaY + batteryAreaHeight / 2, columnWidth / 2
  // );
  // batteryBgGradient.addColorStop(0, 'rgba(0, 255, 136, 0.15)');
  // batteryBgGradient.addColorStop(1, 'rgba(0, 255, 136, 0.05)');
  //
  // context.fillStyle = batteryBgGradient;
  // context.fillRect(rightColumnX - 10, batteryAreaY - 10, columnWidth + 20, batteryAreaHeight);

  // 绘制电量区域边框
  // context.strokeStyle = 'rgba(0, 255, 136, 0.6)';
  // context.lineWidth = 2;
  // context.strokeRect(rightColumnX - 10, batteryAreaY - 10, columnWidth + 20, batteryAreaHeight);

  // 电量标题
  // context.fillStyle = '#00ff88';
  // context.font = `bold ${fontSize + 4}px ${fontFamily}`;
  // context.shadowColor = '#00ff88';
  // context.shadowBlur = 8;
  // context.textAlign = 'center';
  // context.textBaseline = 'top';
  // context.fillText('电池状态', rightColumnX + columnWidth / 2, batteryAreaY + 10);
  // context.shadowBlur = 0;

  // 大型电池图形
  // const largeBatteryX = rightColumnX + 20;
  // const largeBatteryY = batteryAreaY + 50;
  // const largeBatteryWidth = columnWidth - 40;
  // const largeBatteryHeight = 40;

  // drawLargeBattery(context, largeBatteryX, largeBatteryY, largeBatteryWidth, largeBatteryHeight, batteryPercentage);

  // 电量数值显示
  // context.fillStyle = batteryPercentage <= 20 ? '#ff4444' : batteryPercentage <= 50 ? '#ffaa00' : '#44ff44';
  // context.font = `bold ${fontSize + 12}px ${fontFamily}`;
  // context.shadowColor = context.fillStyle;
  // context.shadowBlur = 10;
  // context.textAlign = 'center';
  // context.textBaseline = 'top';
  // context.fillText(`${batteryPercentage}%`, rightColumnX + columnWidth / 2, largeBatteryY + 60);
  // context.shadowBlur = 0;

  // 电量状态文字
  // let statusText = '';
  // if (batteryPercentage <= 20) {
  //   statusText = '低电量';
  //   context.fillStyle = '#ff4444';
  // } else if (batteryPercentage <= 50) {
  //   statusText = '半电';
  //   context.fillStyle = '#ffaa00';
  // } else {
  //   statusText = '满电';
  //   context.fillStyle = '#44ff44';
  // }

  // context.font = `bold ${fontSize}px ${fontFamily}`;
  // context.shadowColor = context.fillStyle;
  // context.shadowBlur = 6;
  // context.textAlign = 'center';
  // context.textBaseline = 'top';
  // context.fillText(statusText, rightColumnX + columnWidth / 2, largeBatteryY + 100);
  // context.shadowBlur = 0;

  // 底部状态指示器
  const statusY = canvas.height - padding - 25;

  // 在线状态指示器
  context.fillStyle = '#44ff44';
  context.shadowColor = '#44ff44';
  context.shadowBlur = 8;
  context.beginPath();
  context.arc(canvas.width / 2 - 50, statusY, 8, 0, Math.PI * 2);
  context.fill();
  context.shadowBlur = 0;

  // 状态文字
  context.fillStyle = '#44ff44';
  context.font = `bold ${fontSize + 2}px ${fontFamily}`;
  context.textAlign = 'center';
  context.textBaseline = 'middle';
  // context.fillText('● SYSTEM ONLINE', canvas.width / 2, statusY);

  // 创建纹理
  const texture = new THREE.CanvasTexture(canvas);
  texture.needsUpdate = true;

  return texture;
};

// 创建3D卡片
const create3DCard = () => {
  if (!props.scene || !props.model.currentPosition) {
    console.warn('Scene or model position not available for 3D card creation');
    return;
  }

  // 如果已存在卡片，先移除
  if (cardGroup) {
    remove3DCard();
  }

  // 创建卡片组
  cardGroup = new THREE.Group();

  // 卡片内容文本
  const deviceType = getDeviceType();
  const vehicleNumber = getVehicleNumber();
  const pos = props.model.currentPosition;
  const rot = props.model.currentRotation || { x: 0, y: 0, z: 0 };
  const battery = props.model.battery || 0;

  const cardText = [
    `${props.model.name} #${vehicleNumber}`,
    `类型: ${deviceType}`,
    `坐标: ${formatCoordinate(pos.x)}, ${formatCoordinate(pos.y)}, ${formatCoordinate(pos.z)}`,
    `角度: ${formatAngle(rot.x)}°, ${formatAngle(rot.y)}°, ${formatAngle(rot.z)}°`,
    `电量: ${battery}% | 状态: 在线`
  ].join('\n');



  // 创建文本纹理
  const textTexture = createTextTexture(cardText, {
    fontSize: 18,
    backgroundColor: 'rgba(0, 20, 40, 0.95)',
    borderColor: '#00ff88',
    borderWidth: 3,
    glowColor: '#00ff88'
  });

  if (!textTexture) {
    console.error('Failed to create text texture');
    return;
  }

  // 创建卡片几何体 (平面) - 更大更高的尺寸
  const cardGeometry = new THREE.PlaneGeometry(8, 6); // 增大卡片尺寸，高度提升一半
  const cardMaterial = new THREE.MeshBasicMaterial({
    map: textTexture,
    transparent: true,
    alphaTest: 0.1,
    side: THREE.FrontSide, // 只显示正面，让朝向效果更明显
    depthWrite: false, // 避免深度冲突
    depthTest: true,
    opacity: 1.0 // 完全不透明，更显眼
  });

  cardMesh = new THREE.Mesh(cardGeometry, cardMaterial);

  // 设置渲染顺序，确保卡片在前面
  cardMesh.renderOrder = 999;

  // 添加到组
  cardGroup.add(cardMesh);

  // 设置初始位置 (在模型坐标系原点的正上方，提高高度)
  const modelPos = getModelWorldPosition();
  cardGroup.position.set(modelPos.x, modelPos.y + 12, modelPos.z); // 从+5提高到+8

  // 初始朝向模型坐标系的Z轴负方向
  updateCardOrientation();

  // 添加到场景
  props.scene.add(cardGroup);

  console.log(`3D卡片已创建: ${props.model.id} at position:`, modelPos);
};

// 获取模型的实际世界位置
const getModelWorldPosition = () => {
  if (props.modelMesh) {
    // 如果有实际的3D模型对象，获取其世界坐标
    const worldPosition = new THREE.Vector3();
    props.modelMesh.getWorldPosition(worldPosition);
    return worldPosition;
  } else if (props.model.currentPosition) {
    // 否则使用模型数据中的位置
    return new THREE.Vector3(
      props.model.currentPosition.x,
      props.model.currentPosition.y,
      props.model.currentPosition.z
    );
  }
  return new THREE.Vector3(0, 0, 0);
};

// 更新卡片朝向相机 - 向日葵原理
const updateCardOrientation = () => {
  if (!cardMesh || !props.modelMesh) return;

  try {
    // 获取模型的世界变换矩阵
    props.modelMesh.updateMatrixWorld();
    const modelMatrix = props.modelMesh.matrixWorld;

    // 获取卡片的世界位置
    const cardWorldPosition = new THREE.Vector3();
    cardMesh.getWorldPosition(cardWorldPosition);

    // 计算模型坐标系的Z轴负方向在世界坐标系中的方向
    // 模型坐标系的Z轴负方向是 (0, 0, -1)
    const modelZNegative = new THREE.Vector3(0, 0, 1);

    // 将模型坐标系的方向向量转换到世界坐标系
    // 只应用旋转，不应用位移
    const modelRotationMatrix = new THREE.Matrix3().setFromMatrix4(modelMatrix);
    const worldZNegative = modelZNegative.clone().applyMatrix3(modelRotationMatrix);

    // 计算卡片应该朝向的目标点
    const lookAtTarget = cardWorldPosition.clone().add(worldZNegative);

    // 设置正确的up向量（Y轴向上）
    cardMesh.up.set(0, 1, 0);

    // 让卡片朝向模型坐标系的Z轴负方向
    cardMesh.lookAt(lookAtTarget);

    // 确保卡片材质更新
    if (cardMesh.material) {
      cardMesh.material.needsUpdate = true;
    }

    // 调试信息（可选）
    // if (Math.random() < 0.01) { // 1%的概率打印，避免日志过多
    //   console.log('🧭 卡片朝向更新 (模型Z轴负方向):', {
    //     cardPosition: cardWorldPosition,
    //     modelZNegative: worldZNegative,
    //     lookAtTarget: lookAtTarget,
    //     modelId: props.model.id,
    //     modelRotation: props.model.currentRotation
    //   });
    // }

  } catch (error) {
    console.error('更新卡片朝向失败:', error);
  }
};

// 更新卡片位置 - 跟随模型实时位置
const updateCardPosition = () => {
  if (!cardGroup) return;

  const modelPos = getModelWorldPosition();
  // 将卡片放置在模型正上方8个单位（提高高度）
  cardGroup.position.set(modelPos.x, modelPos.y + 12, modelPos.z);
};

// 更新卡片内容 - 实时更新坐标和角度信息
const updateCardContent = () => {
  if (!cardMesh || !props.scene) return;

  const modelPos = getModelWorldPosition();
  const deviceType = getDeviceType();
  const vehicleNumber = getVehicleNumber();
  const rot = props.model.currentRotation || { x: 0, y: 0, z: 0 };
  const battery = props.model.battery || 0;

  const consume = props.model.consume || 0;
  const distance = props.model.distance || 0;

  const cardText = [
    `${props.model.name} #${vehicleNumber}`,
    `类型: ${deviceType}`,
    // `坐标: X:${formatCoordinate(modelPos.x)} Y:${formatCoordinate(modelPos.y)} Z:${formatCoordinate(modelPos.z)}`,
    // `角度: X:${formatAngle(rot.x)}° Y:${formatAngle(rot.y)}° Z:${formatAngle(rot.z)}°`,
    // `电量: ${battery}%`, // 电池图形会单独绘制
    `累计耗电: ${consume.toFixed(1)} 千瓦时`,
    `走行累计: ${distance.toFixed(1)} 米`,
    `状态: 自动`
  ].join('\n');

  // 重新创建纹理
  const textTexture = createTextTexture(cardText, {
    fontSize: 48,
    backgroundColor: 'rgba(0, 20, 40, 0.95)',
    borderColor: '#00ff88',
    borderWidth: 3,
    glowColor: '#00ff88'
  });

  if (textTexture && cardMesh.material) {
    // 更新材质的纹理
    if (cardMesh.material.map) {
      cardMesh.material.map.dispose(); // 清理旧纹理
    }
    cardMesh.material.map = textTexture;
    cardMesh.material.needsUpdate = true;
  }
};

// 移除3D卡片
const remove3DCard = () => {
  if (cardGroup && props.scene) {
    props.scene.remove(cardGroup);
    
    // 清理几何体和材质
    cardGroup.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        if (child.geometry) child.geometry.dispose();
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach(material => material.dispose());
          } else {
            child.material.dispose();
          }
        }
      }
    });
    
    cardGroup = null;
    cardMesh = null;
    console.log(`3D卡片已移除: ${props.model.id}`);
  }
};

// 动画循环 - 实时更新卡片位置、朝向和内容
let animationId: number | null = null;
let lastUpdateTime = 0;
let lastOrientationUpdate = 0;
const UPDATE_INTERVAL = 100; // 每100ms更新一次内容，减少性能消耗
const ORIENTATION_INTERVAL = 16; // 每16ms更新一次朝向（约60fps），确保向日葵效果流畅

const startAnimation = () => {
  const animate = (currentTime: number) => {
    if (props.visible && cardMesh) {
      // 每帧更新位置
      updateCardPosition();

      // 高频更新朝向（向日葵效果）
      if (currentTime - lastOrientationUpdate > ORIENTATION_INTERVAL) {
        updateCardOrientation();
        lastOrientationUpdate = currentTime;
      }

      // 定期更新内容（减少性能消耗）
      if (currentTime - lastUpdateTime > UPDATE_INTERVAL) {
        updateCardContent();
        lastUpdateTime = currentTime;
      }

      animationId = requestAnimationFrame(animate);
    }
  };
  animationId = requestAnimationFrame(animate);
};

const stopAnimation = () => {
  if (animationId) {
    cancelAnimationFrame(animationId);
    animationId = null;
  }
};

// 监听可见性变化
watch(() => props.visible, (visible, oldVisible) => {
  console.log(`📋 3D卡片可见性变化 ${props.model.id}:`, {
    oldVisible,
    newVisible: visible,
    hasScene: !!props.scene,
    modelName: props.model.name
  });

  if (visible) {
    if (props.scene) {
      console.log(`📋 创建3D卡片: ${props.model.name}`);
      create3DCard();
      startAnimation();
    } else {
      console.warn(`📋 场景未准备好，无法创建3D卡片: ${props.model.name}`);
    }
  } else {
    console.log(`📋 隐藏3D卡片: ${props.model.name}`);
    stopAnimation();
    remove3DCard();
  }
}, { immediate: true });

// 监听场景变化
watch(() => props.scene, (scene) => {
  console.log(`3D卡片场景变化 ${props.model.id}:`, !!scene);
  if (props.visible && scene) {
    create3DCard();
    startAnimation();
  }
});

// 监听模型位置变化
watch(() => props.model.currentPosition, () => {
  if (props.visible && cardGroup) {
    updateCardPosition();
    updateCardContent();
  }
}, { deep: true });

// 监听模型旋转变化
watch(() => props.model.currentRotation, () => {
  if (props.visible && cardMesh) {
    updateCardContent();
    updateCardOrientation(); // 旋转变化时更新朝向
  }
}, { deep: true });

// 监听电量变化
watch(() => props.model.battery, () => {
  if (props.visible && cardMesh) {
    updateCardContent();
  }
});

// 监听累计耗电量变化
watch(() => props.model.consume, () => {
  if (props.visible && cardMesh) {
    updateCardContent();
  }
});

// 监听走行累计距离变化
watch(() => props.model.distance, () => {
  if (props.visible && cardMesh) {
    updateCardContent();
  }
});

// 监听相机变化 - 深度监听相机位置和旋转
watch(() => props.camera, (newCamera, oldCamera) => {
  if (props.visible && cardMesh && newCamera) {
    console.log('相机对象变化，更新卡片朝向');
    updateCardOrientation();
  }
}, { deep: true });

// 监听模型网格变化（位置、旋转等）
watch(() => props.modelMesh?.matrixWorld, () => {
  if (props.visible && cardMesh && props.modelMesh) {
    updateCardOrientation();
  }
}, { deep: true });

// 组件卸载时清理
onUnmounted(() => {
  stopAnimation();
  remove3DCard();
});
</script>

<template>
  <!-- 这个组件不需要模板，因为它直接操作3D场景 -->
</template>
