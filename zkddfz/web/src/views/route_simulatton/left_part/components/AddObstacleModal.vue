<template>
  <div v-if="visible" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
    <div class="bg-slate-800 rounded-lg border border-cyan-500/30 shadow-xl max-w-md w-full mx-4">
      <!-- 模态框头部 -->
      <div class="flex items-center justify-between p-4 border-b border-slate-700">
        <h3 class="text-lg font-semibold text-white">添加障碍物</h3>
        <button
          @click="closeModal"
          class="text-slate-400 hover:text-white transition-colors duration-200"
        >
          ✕
        </button>
      </div>

      <!-- 模态框内容 -->
      <div class="p-4 space-y-4">
        <!-- 障碍物名称 -->
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">障碍物名称</label>
          <input
            v-model="obstacleForm.name"
            type="text"
            class="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white placeholder-slate-400 focus:border-cyan-500 focus:outline-none"
            placeholder="输入障碍物名称"
          />
        </div>

        <!-- 位置坐标 -->
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">位置坐标</label>
          <div class="grid grid-cols-3 gap-2">
            <div>
              <label class="block text-xs text-slate-400 mb-1">X</label>
              <input
                v-model.number="obstacleForm.position.x"
                type="number"
                step="0.1"
                class="w-full px-2 py-1 bg-slate-700 border border-slate-600 rounded text-white text-sm focus:border-cyan-500 focus:outline-none"
              />
            </div>
            <div>
              <label class="block text-xs text-slate-400 mb-1">Y</label>
              <input
                v-model.number="obstacleForm.position.y"
                type="number"
                step="0.1"
                class="w-full px-2 py-1 bg-slate-700 border border-slate-600 rounded text-white text-sm focus:border-cyan-500 focus:outline-none"
              />
            </div>
            <div>
              <label class="block text-xs text-slate-400 mb-1">Z</label>
              <input
                v-model.number="obstacleForm.position.z"
                type="number"
                step="0.1"
                class="w-full px-2 py-1 bg-slate-700 border border-slate-600 rounded text-white text-sm focus:border-cyan-500 focus:outline-none"
              />
            </div>
          </div>
        </div>

        <!-- 旋转角度 -->
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">旋转角度 (度)</label>
          <div class="grid grid-cols-3 gap-2">
            <div>
              <label class="block text-xs text-slate-400 mb-1">X</label>
              <input
                v-model.number="obstacleForm.rotation.x"
                type="number"
                step="1"
                min="0"
                max="360"
                class="w-full px-2 py-1 bg-slate-700 border border-slate-600 rounded text-white text-sm focus:border-cyan-500 focus:outline-none"
              />
            </div>
            <div>
              <label class="block text-xs text-slate-400 mb-1">Y</label>
              <input
                v-model.number="obstacleForm.rotation.y"
                type="number"
                step="1"
                min="0"
                max="360"
                class="w-full px-2 py-1 bg-slate-700 border border-slate-600 rounded text-white text-sm focus:border-cyan-500 focus:outline-none"
              />
            </div>
            <div>
              <label class="block text-xs text-slate-400 mb-1">Z</label>
              <input
                v-model.number="obstacleForm.rotation.z"
                type="number"
                step="1"
                min="0"
                max="360"
                class="w-full px-2 py-1 bg-slate-700 border border-slate-600 rounded text-white text-sm focus:border-cyan-500 focus:outline-none"
              />
            </div>
          </div>
        </div>

        <!-- 缩放比例 -->
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">缩放比例</label>
          <div class="grid grid-cols-3 gap-2">
            <div>
              <label class="block text-xs text-slate-400 mb-1">X</label>
              <input
                v-model.number="obstacleForm.scale.x"
                type="number"
                step="0.1"
                min="0.1"
                max="5"
                class="w-full px-2 py-1 bg-slate-700 border border-slate-600 rounded text-white text-sm focus:border-cyan-500 focus:outline-none"
              />
            </div>
            <div>
              <label class="block text-xs text-slate-400 mb-1">Y</label>
              <input
                v-model.number="obstacleForm.scale.y"
                type="number"
                step="0.1"
                min="0.1"
                max="5"
                class="w-full px-2 py-1 bg-slate-700 border border-slate-600 rounded text-white text-sm focus:border-cyan-500 focus:outline-none"
              />
            </div>
            <div>
              <label class="block text-xs text-slate-400 mb-1">Z</label>
              <input
                v-model.number="obstacleForm.scale.z"
                type="number"
                step="0.1"
                min="0.1"
                max="5"
                class="w-full px-2 py-1 bg-slate-700 border border-slate-600 rounded text-white text-sm focus:border-cyan-500 focus:outline-none"
              />
            </div>
          </div>
        </div>

        <!-- 颜色设置 -->
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">颜色</label>
          <div class="flex items-center gap-3">
            <input
              v-model="obstacleForm.color"
              type="color"
              class="w-12 h-8 rounded border border-slate-600 bg-slate-700 cursor-pointer"
            />
            <input
              v-model="obstacleForm.color"
              type="text"
              class="flex-1 px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white text-sm focus:border-cyan-500 focus:outline-none"
              placeholder="#ffffff"
            />
          </div>
        </div>

        <!-- 初始电量 -->
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">初始电量 (%)</label>
          <input
            v-model.number="obstacleForm.battery"
            type="number"
            min="0"
            max="100"
            step="1"
            class="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:border-cyan-500 focus:outline-none"
          />
        </div>
      </div>

      <!-- 模态框底部 -->
      <div class="flex items-center justify-end gap-3 p-4 border-t border-slate-700">
        <button
          @click="closeModal"
          class="px-4 py-2 text-sm text-slate-400 hover:text-white transition-colors duration-200"
        >
          取消
        </button>
        <button
          @click="confirmAdd"
          :disabled="!isFormValid"
          :class="[
            'px-4 py-2 text-sm rounded border transition-all duration-200',
            isFormValid
              ? 'bg-orange-500/20 border-orange-400 text-orange-300 hover:bg-orange-500/30'
              : 'bg-slate-600/60 border-slate-500 text-slate-400 cursor-not-allowed'
          ]"
        >
          添加障碍物
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['close', 'confirm']);

// 障碍物表单数据
const obstacleForm = ref({
  name: '',
  position: { x: 0, y: 0, z: 0 },
  rotation: { x: 0, y: 0, z: 0 },
  scale: { x: 1, y: 1, z: 1 },
  color: '#ff6b35',
  battery: 100
});

// 表单验证
const isFormValid = computed(() => {
  return obstacleForm.value.name.trim().length > 0;
});

// 监听模态框显示状态，重置表单
watch(() => props.visible, (newValue) => {
  if (newValue) {
    resetForm();
  }
});

// 重置表单
const resetForm = () => {
  obstacleForm.value = {
    name: `障碍物_${Date.now().toString().slice(-4)}`,
    position: { 
      x: Math.round((Math.random() * 100 - 50) * 10) / 10, 
      y: 0, 
      z: Math.round((Math.random() * 100 - 50) * 10) / 10 
    },
    rotation: { x: 0, y: Math.round(Math.random() * 360), z: 0 },
    scale: { x: 1, y: 1, z: 1 },
    color: '#ff6b35',
    battery: 100
  };
};

// 关闭模态框
const closeModal = () => {
  emit('close');
};

// 确认添加
const confirmAdd = () => {
  if (!isFormValid.value) return;
  
  // 将角度转换为弧度
  const obstacleData = {
    ...obstacleForm.value,
    rotation: {
      x: (obstacleForm.value.rotation.x * Math.PI) / 180,
      y: (obstacleForm.value.rotation.y * Math.PI) / 180,
      z: (obstacleForm.value.rotation.z * Math.PI) / 180
    }
  };
  
  emit('confirm', obstacleData);
  closeModal();
};
</script>

<style scoped>
/* 自定义颜色选择器样式 */
input[type="color"] {
  -webkit-appearance: none;
  border: none;
  cursor: pointer;
}

input[type="color"]::-webkit-color-swatch-wrapper {
  padding: 0;
}

input[type="color"]::-webkit-color-swatch {
  border: none;
  border-radius: 4px;
}
</style>
