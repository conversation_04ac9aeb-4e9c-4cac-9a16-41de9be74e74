#!/bin/bash

# 配置参数
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly WORKSPACE_SCRIPT="scripts/generate-workspace"
readonly WEB_DIR="web"

# VS Code 扩展列表
readonly EXTENSIONS=(
    "bradlc.vscode-tailwindcss"
    "esbenp.prettier-vscode"
    "vmware.vscode-boot-dev-pack"
    "vscjava.vscode-java-pack"
    "vue.volar"
)

# 全局变量
NODE_VERSION=""
NODE_DIR=""
NODE_EXECUTABLE=""
PNPM_EXECUTABLE=""

# 检测操作系统
detect_os() {
    case "$(uname -s)" in
        MINGW*|MSYS*|CYGWIN*)
            echo "windows"
            ;;
        Darwin*)
            echo "macos"
            ;;
        Linux*)
            echo "linux"
            ;;
        *)
            echo "unknown"
            ;;
    esac
}

# 日志输出函数
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $*" >&2
}

# 检查扩展是否已安装
is_extension_installed() {
    local extension="$1"
    code --list-extensions 2>/dev/null | grep -i "^${extension}$" >/dev/null 2>&1
}

# 检查项目名称并请求确认
check_project_confirmation() {
    local current_folder_name
    current_folder_name=$(basename "$SCRIPT_DIR")
    
    if [ "$current_folder_name" = "equip4j" ]; then
        echo "========================================"
        echo "    检测到项目目录名称为 equip4j"
        echo "========================================"
        echo ""
        echo "建议将目录名称改为项目名, 便于团队协作"
        echo ""
        echo -n "确认继续？按 Enter 键继续，或按 Ctrl+C 退出后重命名..."
        read -r
        echo ""
        echo "继续执行初始化..."
        echo ""
    fi
}

# 自动检测 Node.js 版本
detect_node_version() {
    log "检测 Node.js 版本..."
    
    local runtime_dir="$SCRIPT_DIR/runtime"
    local os_type
    os_type=$(detect_os)
    
    if [ ! -d "$runtime_dir" ]; then
        log "错误: runtime 目录不存在"
        return 1
    fi
    
    # 查找所有匹配 node-v* 模式的目录，并按版本排序
    local node_dirs
    node_dirs=$(find "$runtime_dir" -maxdepth 1 -type d -name "node-v*" | sort -V)
    
    if [ -z "$node_dirs" ]; then
        log "错误: 在 runtime 目录中未找到任何 Node.js 版本"
        log "当前 runtime 目录内容:"
        ls -la "$runtime_dir" 2>/dev/null | sed 's/^/  /'
        return 1
    fi
    
    # 选择最后一个（最新的）版本
    NODE_DIR=$(echo "$node_dirs" | tail -n 1)
    NODE_VERSION=$(basename "$NODE_DIR" | sed 's/^node-v//')
    
    # 根据操作系统设置可执行文件路径
    case "$os_type" in
        "windows")
            NODE_EXECUTABLE="$NODE_DIR/node.exe"
            PNPM_EXECUTABLE="$NODE_DIR/pnpm.exe"
            ;;
        "macos"|"linux")
            NODE_EXECUTABLE="$NODE_DIR/bin/node"
            PNPM_EXECUTABLE="$NODE_DIR/bin/pnpm"
            ;;
        *)
            log "错误: 不支持的操作系统: $os_type"
            return 1
            ;;
    esac
    
    # 验证 Node.js 可执行文件存在
    if [ ! -f "$NODE_EXECUTABLE" ]; then
        log "错误: Node.js 可执行文件不存在: $NODE_EXECUTABLE"
        log "目录内容:"
        ls -la "$NODE_DIR" 2>/dev/null | sed 's/^/  /'
        return 1
    fi
    
    # 验证 pnpm 可执行文件存在
    if [ ! -f "$PNPM_EXECUTABLE" ]; then
        log "错误: pnpm 可执行文件不存在: $PNPM_EXECUTABLE"
        log "目录内容:"
        ls -la "$NODE_DIR" 2>/dev/null | sed 's/^/  /'
        return 1
    fi
    
    log "✓ 检测到操作系统: $os_type"
    log "✓ 检测到 Node.js 版本: $NODE_VERSION"
    log "✓ Node.js 路径: $NODE_EXECUTABLE"
    log "✓ pnpm 路径: $PNPM_EXECUTABLE"
    
    return 0
}

# 检查 VS Code 是否可用
check_vscode() {
    log "[3/5] 检查 VS Code..."
    if ! command -v code >/dev/null 2>&1; then
        log "错误: 在系统路径中未找到 VS Code 的 'code' 命令"
        echo ""
        echo "可能的解决方案:"
        echo "1. 确保 VS Code 已安装"
        echo "2. 重新安装 VS Code 并勾选 \"Add to PATH\""
        echo "3. 手动添加 VS Code 到系统 PATH"
        echo ""
        return 1
    fi
    log "✓ VS Code 检查通过"
    return 0
}

# 安装 VS Code 扩展
install_extensions() {
    log "[4/5] 检查和安装 VS Code 扩展..."
    
    local installed_count=0
    local skipped_count=0
    local failed_count=0
    
    # 检查是否可以获取已安装扩展列表
    local can_check_installed=true
    if ! code --list-extensions >/dev/null 2>&1; then
        log "警告: 无法获取已安装扩展列表"
        can_check_installed=false
    fi
    
    for ext in "${EXTENSIONS[@]}"; do
        local need_install=true
        
        # 如果可以检查已安装扩展，则检查是否已安装
        if [ "$can_check_installed" = true ]; then
            if is_extension_installed "$ext"; then
                log "  ✓ 已安装: $ext"
                need_install=false
                ((skipped_count++))
            fi
        fi
        
        # 如果需要安装
        if [ "$need_install" = true ]; then
            log "  正在安装: $ext"
            if code --install-extension "$ext" >/dev/null 2>&1; then
                log "  ✓ 安装成功: $ext"
                ((installed_count++))
            else
                log "  ✗ 安装失败: $ext"
                ((failed_count++))
            fi
        fi
    done
    
    echo ""
    echo "扩展安装统计:"
    echo "  新安装: $installed_count"
    echo "  已存在: $skipped_count"
    echo "  失败: $failed_count"
    
    if [ $failed_count -gt 0 ]; then
        log "✗ 部分扩展安装失败，但这不影响工作区的正常使用"
    else
        log "✓ 所有扩展检查完成"
    fi
}

# 安装 Web 依赖
install_web_deps() {
    log "[2/5] 安装 Web 依赖..."
    
    local web_dir="$SCRIPT_DIR/$WEB_DIR"
    
    # 检查 web 目录是否存在
    if [ ! -d "$web_dir" ]; then
        log "跳过: Web 目录不存在 ($WEB_DIR)"
        return 0
    fi
    
    # 检查是否有 package.json
    if [ ! -f "$web_dir/package.json" ]; then
        log "跳过: 在 $WEB_DIR 目录中未找到 package.json"
        return 0
    fi
    
    log "开始安装 Web 依赖..."

    # 切换到 web 目录
    local original_dir
    original_dir=$(pwd)
    cd "$web_dir" || {
        log "错误: 无法切换到目录 $web_dir"
        return 1
    }

    log "使用 pnpm 路径: $PNPM_EXECUTABLE"
    log "使用 pnpm 安装依赖..."
    if "$PNPM_EXECUTABLE" install; then
        log "✓ Web 依赖安装成功"
    else
        log "✗ pnpm 安装失败"
        cd "$original_dir"
        return 1
    fi

    # 切换回原目录
    cd "$original_dir"
}

# 检查 runtime 目录
check_runtime() {
    log "[1/5] 检查 runtime 环境..."
    
    local original_dir
    original_dir=$(pwd)

    cd "$SCRIPT_DIR"

    if [ ! -d "runtime" ]; then
        log "✗ runtime 文件夹不存在，请下载后重新执行"
        exit 1
    fi
    
    # 检测 Node.js 版本
    if ! detect_node_version; then
        exit 1
    fi
    
    log "✓ runtime 环境检查通过"

    cd "$original_dir"
}

# 主执行流程
main() {
    # 首先检查项目名称并请求确认
    check_project_confirmation
    
    echo "========================================"
    echo "    VS Code 工作区设置脚本"
    echo "========================================"
    echo ""
    
    # 检查环境
    check_runtime

    # 安装 Web 依赖
    if ! install_web_deps; then
        log "警告: Web 依赖安装失败，但继续执行其他步骤"
    fi

    # 切换到脚本目录
    cd "$SCRIPT_DIR"

    # 检查 VS Code 是否可用
    if ! check_vscode; then
        exit 1
    fi
    
    # 安装 VS Code 扩展
    install_extensions
    
    # 生成工作区配置
    echo ""
    log "[5/5] 生成工作区配置..."
    
    # 检查工作区脚本是否存在
    local workspace_script_path="$WORKSPACE_SCRIPT"
    if [ ! -f "$workspace_script_path" ]; then
        if [ -f "$workspace_script_path.js" ]; then
            workspace_script_path="$workspace_script_path.js"
        else
            log "✗ 错误: 工作区生成脚本未找到"
            echo "  查找路径: $WORKSPACE_SCRIPT"
            echo "  查找路径: $WORKSPACE_SCRIPT.js"
            echo ""
            echo "请确保以下文件之一存在:"
            echo "  - $WORKSPACE_SCRIPT"
            echo "  - $WORKSPACE_SCRIPT.js"
            exit 1
        fi
    fi
    
    log "✓ 使用脚本: $workspace_script_path"
    log "✓ 使用Node.js: $NODE_EXECUTABLE"
    
    # 使用检测到的 Node.js 版本运行工作区生成脚本
    log "正在执行工作区生成脚本..."
    if ! "$NODE_EXECUTABLE" "$workspace_script_path"; then
        local script_exit_code=$?
        log "✗ 错误: 工作区生成失败 (退出代码: $script_exit_code)"
        echo ""
        echo "调试信息:"
        echo "  Node.js路径: $NODE_EXECUTABLE"
        echo "  脚本路径: $workspace_script_path"
        echo "  当前目录: $(pwd)"
        echo ""
        echo "手动测试命令:"
        echo "  \"$NODE_EXECUTABLE\" \"$workspace_script_path\""
        echo ""
        exit 1
    fi
    
    # 获取当前文件夹名称并构建工作区文件名
    local current_folder_name
    current_folder_name=$(basename "$(pwd)")
    local workspace_file="${current_folder_name}.code-workspace"
    
    # 检查工作区文件是否存在
    if [ ! -f "$workspace_file" ]; then
        log "✗ 错误: 工作区文件未找到: $workspace_file"
        echo ""
        echo "当前目录中的 .code-workspace 文件:"
        local found_workspace=""
        for ws_file in *.code-workspace; do
            if [ -f "$ws_file" ]; then
                echo "  - $ws_file"
                if [ -z "$found_workspace" ]; then
                    found_workspace="$ws_file"
                fi
            fi
        done
        
        if [ -n "$found_workspace" ]; then
            log "✓ 使用找到的工作区文件: $found_workspace"
            workspace_file="$found_workspace"
        else
            echo "  未找到任何 .code-workspace 文件"
            exit 1
        fi
    fi
}

# 执行主函数
main "$@"
