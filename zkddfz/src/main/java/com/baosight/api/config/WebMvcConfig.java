package com.baosight.api.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.baosight.api.constants.ApiPaths;
import com.baosight.api.feature.admin.interceptor.AdminRoleInterceptor;
import com.baosight.api.feature.user.interceptor.AccessTokenInterceptor;

import lombok.RequiredArgsConstructor;

@Configuration
@RequiredArgsConstructor
public class WebMvcConfig implements WebMvcConfigurer {
  private final AccessTokenInterceptor accessTokenInterceptor;

  private final AdminRoleInterceptor adminRoleInterceptor;

  @Override
  public void addInterceptors(@NonNull InterceptorRegistry registry) {
    registry.addInterceptor(accessTokenInterceptor)
        .addPathPatterns(ApiPaths.API_V1_PATTERN)
        .excludePathPatterns(ApiPaths.User.test,ApiPaths.User.LOGIN, ApiPaths.User.LOGOUT);

    registry.addInterceptor(adminRoleInterceptor)
        .addPathPatterns(ApiPaths.Admin.PATTERN);
  }
}
