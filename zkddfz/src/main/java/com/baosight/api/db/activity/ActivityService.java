package com.baosight.api.db.activity;

import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ActivityService {
  private final ActivityRepository activityRepository;

  public void log(String username, String message) {
    if (username == null) {
      throw new IllegalArgumentException("用户名不能为空");
    }
    if (message == null) {
      throw new IllegalArgumentException("操作记录不能为空");
    }

    ActivityEntity activity = new ActivityEntity();

    activity.setUsername(username);
    activity.setActivity(message);

    activityRepository.save(activity);
  }
}
