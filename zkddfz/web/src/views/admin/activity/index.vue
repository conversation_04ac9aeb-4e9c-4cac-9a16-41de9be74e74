<template>
  <div class="w-full p-2 space-y-2">
    <search ref="search" @search="handleSearch" />

    <a-divider />

    <toolbar
      :has-next-page="hasNextPage"
      :table-id="tableId"
      @fetch-next-page="fetchNextPage"
      @filter="quickFilter"
      ref="toolbar"
    />

    <ag-grid
      :id="tableId"
      :column-defs="columnDefs"
      :row-data="rowData"
      :style="{ height }"
      @first-data-rendered="gridApi?.sizeColumnsToFit()"
      @grid-ready="setGridApi"
    />
  </div>
</template>

<script setup lang="ts">
import type { ConditionMap } from '@/common/types';
import { AgGrid, useGridApi } from '@/components/ag-grid';
import { useInfiniteQuery } from '@tanstack/vue-query';
import { useElementSize } from '@vueuse/core';
import { flatten, last } from 'lodash';
import { computed, ref } from 'vue';
import Search from './components/search.vue';
import Toolbar from './components/toolbar.vue';
import { pageLimit, tableId } from './libraries/constants';
import { createColumnDefs, getData } from './libraries/services';

const toolbar = ref();

const search = ref();

const { height: toolbarHeight } = useElementSize(toolbar);

const { height: searchHeight } = useElementSize(search);

const height = computed(() => `calc(100vh - ${toolbarHeight.value}px - ${searchHeight.value}px - 92px)`);

const [gridApi, setGridApi] = useGridApi();

const quickFilter = (value: string) => {
  gridApi.value?.setGridOption('quickFilterText', value);
};

const columnDefs = createColumnDefs();

const conditionMap = ref<ConditionMap>();

const handleSearch = (payload: ConditionMap) => {
  conditionMap.value = payload;
};

const {
  data: queryData,
  hasNextPage,
  fetchNextPage,
} = useInfiniteQuery({
  queryKey: [tableId, conditionMap],
  queryFn: ({ pageParam }) => getData(conditionMap, pageParam, pageLimit),
  initialPageParam: 0,
  getNextPageParam: lastPage => {
    if (lastPage.length < pageLimit) {
      return undefined;
    }
    return last(lastPage)?.id ?? undefined;
  },
});

const rowData = computed(() => flatten(queryData.value?.pages));
</script>
