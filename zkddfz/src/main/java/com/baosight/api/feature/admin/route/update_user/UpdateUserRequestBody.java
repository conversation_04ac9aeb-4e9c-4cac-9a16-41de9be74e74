package com.baosight.api.feature.admin.route.update_user;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class UpdateUserRequestBody {
  @NotBlank(message = "用户名不能为空")
  private String username;

  @Size(min = 1, message = "姓名不能为空")
  private String displayName;

  @Size(min = 6, message = "密码长度不能小于 6 个字符")
  private String password;
}
