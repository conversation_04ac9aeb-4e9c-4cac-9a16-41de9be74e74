package com.baosight.api.controller;

import com.baosight.api.constants.ApiPaths;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController("")
public class TestController {

    @PostMapping(ApiPaths.User.test)
public List<Map<String,Object>> test(){
        Map<String, Object> map = new HashMap<>();
        List<Map<String, Object>> maps = new ArrayList<>();
        map.put("id", 1);
        maps.add(map);
        return maps;
    }
}
