import { message } from 'ant-design-vue';
import axios, { AxiosError, isAxiosError } from 'axios';

export const baseURL = import.meta.env.BASE_URL;

export const api = axios.create({ baseURL, withCredentials: true });

const getAxiosErrorMessage = (error: AxiosError) => {
  if (error.code === 'ERR_NETWORK') {
    return '无法获取数据';
  }

  if (error.response?.status === 401) {
    return '请求未授权';
  }

  // { data: object }
  if (error.response?.data && typeof error.response.data === 'object') {
    if ('message' in error.response.data && typeof error.response.data.message === 'string') {
      // data: { message: string }
      return error.response.data.message;
    }

    // { data: object }
    return JSON.stringify(error.response.data);
  }

  // { data: string }
  if (error.response?.data && typeof error.response.data === 'string') {
    return error.response.data;
  }

  return error.message;
};

const notifyResponseError = (error: unknown) => {
  console.error(error);

  if (isAxiosError(error)) {
    message.error(getAxiosErrorMessage(error));
  } else if (error instanceof Error) {
    message.error(error.message);
  }

  return Promise.reject(error);
};

api.interceptors.response.use(response => response, notifyResponseError);
