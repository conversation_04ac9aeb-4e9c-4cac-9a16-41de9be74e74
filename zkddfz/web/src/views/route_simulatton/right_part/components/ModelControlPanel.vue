<script setup lang="ts">
import { computed, ref, watch } from 'vue';

interface Model {
  id: string;
  name: string;
  status: 'loaded' | 'loading' | 'error';
}

interface Props {
  availableModels: Model[];
  sceneManagerRef?: any;
}

const props = defineProps<Props>();
const emit = defineEmits(['model-selected', 'movement-started', 'movement-stopped']);

const selectedModelId = ref<string | null>(props.availableModels[0]?.id || null);
const selectedModelStatus = ref<string | null>(props.availableModels[0]?.status || 'loading');
const selectedModel = computed(() => props.availableModels.find(model => model.id === selectedModelId.value));
const targetPosition = ref({ x: 0, y: 0, z: 0 });
const moveSpeed = ref(2);
const enableKeyboardControl = ref(false);
const stepLength = ref(1);
const modelObjectChecked = ref(true);



// 收起展开状态
const isExpanded = ref(true);

// 切换展开状态
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value;
  console.log('🔄 移动控制卡片', isExpanded.value ? '展开' : '收起');
};

// Set initial selected model if availableModels changes
watch(
  () => props.availableModels,
  newModels => {
    if (!selectedModelId.value && newModels.length > 0) {
      selectedModelId.value = newModels[0].id;
      emit('model-selected', newModels[0].id);
    } else if (selectedModelId.value && !newModels.some(m => m.id === selectedModelId.value)) {
      // If selected model is removed, default to first available or null
      const newSelectedId = newModels.length > 0 ? newModels[0].id : null;
      selectedModelId.value = newSelectedId;
      emit('model-selected', newSelectedId);
    }
  },
  { immediate: true },
);

// Watch for selectedModelId changes and emit event
watch(selectedModelId, (newId) => {
  if (newId) {
    emit('model-selected', newId);
  }
});

const handleGetPosition = () => {
  console.log('获取当前位置', selectedModelId.value);

  if (!selectedModelId.value) {
    console.warn('没有选中的模型');
    return;
  }

  if (!props.sceneManagerRef) {
    console.warn('SceneManager 不可用');
    return;
  }

  // 获取模型当前位置
  const position = props.sceneManagerRef.getModelPosition(selectedModelId.value);
  if (position) {
    // 更新目标位置输入框
    targetPosition.value = {
      x: Math.round(position.x * 100) / 100,
      y: Math.round(position.y * 100) / 100,
      z: Math.round(position.z * 100) / 100,
    };
    console.log('获取到当前位置:', position);
  } else {
    console.warn('无法获取模型位置');
  }
};

const handleStartMove = () => {
  console.log('开始移动', selectedModelId.value, targetPosition.value, moveSpeed.value);

  if (!selectedModelId.value) {
    alert('请选择一个模型');
    return;
  }

  if (!props.sceneManagerRef) {
    console.warn('SceneManager 不可用');
    return;
  }

  // 调用SceneManager的移动方法
  try {
    props.sceneManagerRef.moveModelToPosition(
      selectedModelId.value,
      targetPosition.value,
      moveSpeed.value
    );
    emit('movement-started', {
      modelId: selectedModelId.value,
      targetPosition: targetPosition.value,
      speed: moveSpeed.value
    });
    console.log('移动命令已发送');
  } catch (error) {
    console.error('移动失败:', error);
    alert('移动失败，请检查控制台');
  }
};

const handleStopMove = () => {
  console.log('停止移动', selectedModelId.value);

  if (!selectedModelId.value) {
    console.warn('没有选中的模型');
    return;
  }

  if (!props.sceneManagerRef) {
    console.warn('SceneManager 不可用');
    return;
  }

  // 调用SceneManager的停止移动方法
  try {
    props.sceneManagerRef.stopModelMovement(selectedModelId.value);
    emit('movement-stopped', { modelId: selectedModelId.value });
    console.log('停止移动命令已发送');
  } catch (error) {
    console.error('停止移动失败:', error);
  }
};


</script>

<template>
  <div
    class="card_box relative overflow-hidden group bg-gradient-to-br from-slate-800/80 to-slate-700/60 w-full"
  >
  
    <div class="txt_title flex items-center justify-between">
      <h2 class="font-semibold">移动控制</h2>
    </div>
    <!-- Card Content -->
    <div class="relative z-10 p-4 pt-2">
      <!-- Header -->
      <!-- 可收起的内容区域 -->
      <div
        class="transition-all duration-300 ease-in-out overflow-hidden"
        :class="isExpanded ? 'max-h-[1000px] opacity-100' : 'max-h-0 opacity-0'"
      >
        <!-- Model Selection -->
      <div class="mb-2">
        <label for="model-select" class="block text-xs font-medium text-slate-400 mb-2">选择模型:</label>
        <div class="relative">
          <select
            id="model-select"
            v-model="selectedModelId"
            class="futuristic-input-symmetric w-full px-3 py-2 bg-slate-700/60 border border-cyan-500/30 text-white text-sm appearance-none pr-8 focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200 hover:border-cyan-500/50"
          >
            <option v-if="!selectedModelId" value="" disabled>请选择模型</option>
            <option v-for="model in availableModels" :key="model.id" :value="model.id" class="bg-slate-800 text-white">
              {{ model.name }}
            </option>
          </select>
          <span class="absolute right-3 top-1/2 -translate-y-1/2 text-cyan-400/70 pointer-events-none text-xs">▼</span>
        </div>
      </div>

      <!-- Selected Model Info -->
      <div
        v-if="selectedModel"
        class="mb-4 p-3 bg-slate-700/40 border border-cyan-500/20 rounded text-xs text-slate-300"
      >
        <div class="flex flex-wrap gap-2 mb-3">
          <span><span class="font-semibold text-cyan-400">ID:</span> {{ selectedModel.id }}</span>
          <span
            ><span class="font-semibold text-cyan-400">状态:</span>
            <span
              :class="{
                'text-green-400': selectedModel.status === 'loaded',
                'text-yellow-400': selectedModel.status === 'loading',
                'text-red-400': selectedModel.status === 'error',
              }"
              >{{ selectedModel.status }}</span
            >
          </span>
          <span><span class="font-semibold text-cyan-400">对象:</span> <span class="text-green-400">✓</span></span>
        </div>


      </div>

      <!-- Target Position -->
      <div class="mb-2">
        <h3 class="text-sm font-semibold mb-3 text-white">目标位置 (地面坐标系):</h3>
        <div class="grid grid-cols-3 gap-2">
          <div>
            <label for="pos-x" class="block text-xs font-medium text-slate-400 mb-1">X (左右):</label>
            <input
              id="pos-x"
              type="number"
              v-model.number="targetPosition.x"
              class="futuristic-input-symmetric w-full px-3 py-2 bg-slate-700/60 border border-cyan-500/30 text-white text-sm placeholder-slate-400 focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200 hover:border-cyan-500/50"
            />
          </div>
          <div>
            <label for="pos-y" class="block text-xs font-medium text-slate-400 mb-1">Y (高度):</label>
            <input
              id="pos-y"
              type="number"
              v-model.number="targetPosition.y"
              class="futuristic-input-symmetric w-full px-3 py-2 bg-slate-700/60 border border-cyan-500/30 text-white text-sm placeholder-slate-400 focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200 hover:border-cyan-500/50"
            />
          </div>
          <div>
            <label for="pos-z" class="block text-xs font-medium text-slate-400 mb-1">Z (前后):</label>
            <input
              id="pos-z"
              type="number"
              v-model.number="targetPosition.z"
              class="futuristic-input-symmetric w-full px-3 py-2 bg-slate-700/60 border border-cyan-500/30 text-white text-sm placeholder-slate-400 focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200 hover:border-cyan-500/50"
            />
          </div>
        </div>
      </div>

      <!-- Move Speed -->
      <div class="mb-2">
        <label for="move-speed" class="block text-sm font-semibold text-white mb-2">移动速度 (单位/秒):</label>
        <input
          id="move-speed"
          type="number"
          v-model.number="moveSpeed"
          class="futuristic-input-symmetric w-full px-3 py-2 bg-slate-700/60 border border-cyan-500/30 text-white text-sm placeholder-slate-400 focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200 hover:border-cyan-500/50"
        />
      </div>

      <!-- Keyboard Control -->
      <div class="mb-2">
        <div class="flex items-center justify-between">
          <h3 class="text-sm font-semibold mb-3 text-white">键盘控制:</h3>
            <!-- Enable Keyboard Control -->
          <div
            class="control-btn-symmetric w-80 relative overflow-hidden group bg-gradient-to-r from-slate-700/60 to-slate-600/40 border border-cyan-500/30 p-3 transition-all duration-300 hover:from-slate-600/70 hover:to-slate-500/50 hover:border-cyan-400/50 cursor-pointer"
          >
            <div class="flex items-center space-x-3 relative z-10">
              <div class="relative">
                <input type="checkbox" id="enable-keyboard-control" v-model="enableKeyboardControl" class="sr-only" />
                <div
                  class="w-5 h-5 border-2 transition-all duration-200 flex items-center justify-center"
                  :class="
                    enableKeyboardControl
                      ? 'bg-cyan-500/20 border-cyan-400 shadow-sm shadow-cyan-400/50'
                      : 'border-cyan-500/50 bg-slate-800/60 hover:border-cyan-400/70'
                  "
                  @click="enableKeyboardControl = !enableKeyboardControl"
                >
                  <div
                    v-if="enableKeyboardControl"
                    class="w-2 h-2 bg-cyan-400 transition-all duration-200 shadow-sm shadow-cyan-400/50"
                  ></div>
                </div>
              </div>
              <label
                for="enable-keyboard-control"
                class="text-sm font-medium text-slate-200 cursor-pointer flex-1 select-none"
              >
                启用键盘控制
              </label>
            </div>
            <!-- Button Glow -->
            <div
              class="absolute inset-0 bg-gradient-to-r from-cyan-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm pointer-events-none"
            ></div>
          </div>
        </div>
        <div class="space-y-3">

          <!-- Step Length -->
          <div>
            <label for="step-length" class="block text-xs font-medium text-slate-400 mb-2">步长:</label>
            <input
              id="step-length"
              type="number"
              v-model.number="stepLength"
              class="futuristic-input-symmetric w-full px-3 py-2 bg-slate-700/60 border border-cyan-500/30 text-white text-sm placeholder-slate-400 focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200 hover:border-cyan-500/50"
            />
          </div>
        </div>
      </div>

      <!-- Control Buttons -->
      <div class="flex gap-3">
        <button
          @click="handleGetPosition"
          class="control-btn-symmetric w-full h-10 relative overflow-hidden group bg-gradient-to-r from-cyan-600/50 to-cyan-500/30 border border-cyan-500 text-cyan-300 hover:from-cyan-600/70 hover:to-cyan-500/50 transition-all duration-300 font-medium text-sm"
        >
          <span class="relative z-10">获取当前位置</span>
          <!-- Button Glow -->
          <div
            class="absolute inset-0 bg-gradient-to-r from-cyan-400/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
          ></div>
        </button>

        <button
          @click="handleStartMove"
          class="control-btn-symmetric w-full h-10 relative overflow-hidden group bg-gradient-to-r from-green-600/50 to-green-500/30 border border-green-500 text-green-300 hover:from-green-600/70 hover:to-green-500/50 transition-all duration-300 font-medium text-sm"
        >
          <span class="relative z-10">开始移动</span>
          <!-- Button Glow -->
          <div
            class="absolute inset-0 bg-gradient-to-r from-green-400/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
          ></div>
        </button>

        <button
          @click="handleStopMove"
          class="control-btn-symmetric w-full h-10 relative overflow-hidden group bg-gradient-to-r from-red-600/50 to-red-500/30 border border-red-500 text-red-400 hover:from-red-600/70 hover:to-red-500/50 transition-all duration-300 font-medium text-sm"
        >
          <span class="relative z-10">停止移动</span>
          <!-- Button Glow -->
          <div
            class="absolute inset-0 bg-gradient-to-r from-red-400/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
          ></div>
        </button>
      </div>
      </div> <!-- 可收起内容区域结束 -->
    </div>
  </div>
</template>

<style scoped>
/* Only keeping essential clip-path styles that can't be done with Tailwind */
.futuristic-card-symmetric {
  clip-path: polygon(1.5rem 0, 100% 0, 100% calc(100% - 1.5rem), calc(100% - 1.5rem) 100%, 0 100%, 0 1.5rem);
}

.futuristic-input-symmetric {
  clip-path: polygon(0.5rem 0, 100% 0, 100% calc(100% - 0.5rem), calc(100% - 0.5rem) 100%, 0 100%, 0 0.5rem);
}

.control-btn-symmetric {
  clip-path: polygon(0.75rem 0, 100% 0, 100% calc(100% - 0.75rem), calc(100% - 0.75rem) 100%, 0 100%, 0 0.75rem);
}
</style>
<style scoped>
.txt_title {
  background-image: url('../../../../assets/img/text_title.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  width: 100%;
  height: 40px;
  padding-left: 60px;
  padding-right: 10px;
  border-radius: 10px 0px 0px 0px;
}
.txt_title h2 {
  font-size: 18px !important;
  font-weight: 600;
  color: #fff;
}
.card_box{
  border-radius: 10px;
  margin-top: 0px;
}
</style>
