/**
 * 动画工具函数
 * 提供各种缓动函数和动画辅助功能
 */

// 缓动函数集合
export const EasingFunctions = {
  // 线性
  linear: (t) => t,
  
  // 二次方缓动
  easeInQuad: (t) => t * t,
  easeOutQuad: (t) => t * (2 - t),
  easeInOutQuad: (t) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
  
  // 三次方缓动
  easeInCubic: (t) => t * t * t,
  easeOutCubic: (t) => (--t) * t * t + 1,
  easeInOutCubic: (t) => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1,
  
  // 四次方缓动
  easeInQuart: (t) => t * t * t * t,
  easeOutQuart: (t) => 1 - (--t) * t * t * t,
  easeInOutQuart: (t) => t < 0.5 ? 8 * t * t * t * t : 1 - 8 * (--t) * t * t * t,
  
  // 弹性缓动
  easeInElastic: (t) => {
    if (t === 0) return 0;
    if (t === 1) return 1;
    const p = 0.3;
    const a = 1;
    const s = p / 4;
    return -(a * Math.pow(2, 10 * (t -= 1)) * Math.sin((t - s) * (2 * Math.PI) / p));
  },
  
  easeOutElastic: (t) => {
    if (t === 0) return 0;
    if (t === 1) return 1;
    const p = 0.3;
    const a = 1;
    const s = p / 4;
    return a * Math.pow(2, -10 * t) * Math.sin((t - s) * (2 * Math.PI) / p) + 1;
  },
  
  // 回弹缓动
  easeInBounce: (t) => 1 - EasingFunctions.easeOutBounce(1 - t),
  
  easeOutBounce: (t) => {
    if (t < (1 / 2.75)) {
      return 7.5625 * t * t;
    } else if (t < (2 / 2.75)) {
      return 7.5625 * (t -= (1.5 / 2.75)) * t + 0.75;
    } else if (t < (2.5 / 2.75)) {
      return 7.5625 * (t -= (2.25 / 2.75)) * t + 0.9375;
    } else {
      return 7.5625 * (t -= (2.625 / 2.75)) * t + 0.984375;
    }
  }
};

/**
 * 创建动画对象
 * @param {Object} options 动画配置
 * @param {number} options.duration 动画持续时间（毫秒）
 * @param {Function} options.easing 缓动函数
 * @param {Function} options.onUpdate 更新回调
 * @param {Function} options.onComplete 完成回调
 * @returns {Object} 动画控制对象
 */
export function createAnimation(options) {
  const {
    duration = 1000,
    easing = EasingFunctions.easeInOutCubic,
    onUpdate = () => {},
    onComplete = () => {}
  } = options;
  
  let startTime = null;
  let animationId = null;
  let isRunning = false;
  let isPaused = false;
  let pausedTime = 0;
  
  const animate = (currentTime) => {
    if (!startTime) startTime = currentTime - pausedTime;
    
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);
    const easedProgress = easing(progress);
    
    onUpdate(easedProgress, progress);
    
    if (progress < 1 && isRunning) {
      animationId = requestAnimationFrame(animate);
    } else if (progress >= 1) {
      isRunning = false;
      onComplete();
    }
  };
  
  return {
    start() {
      if (!isRunning) {
        isRunning = true;
        isPaused = false;
        animationId = requestAnimationFrame(animate);
      }
    },
    
    stop() {
      isRunning = false;
      isPaused = false;
      if (animationId) {
        cancelAnimationFrame(animationId);
        animationId = null;
      }
      startTime = null;
      pausedTime = 0;
    },
    
    pause() {
      if (isRunning && !isPaused) {
        isPaused = true;
        isRunning = false;
        if (animationId) {
          cancelAnimationFrame(animationId);
          animationId = null;
        }
        pausedTime = performance.now() - startTime;
      }
    },
    
    resume() {
      if (isPaused) {
        isPaused = false;
        isRunning = true;
        startTime = null;
        animationId = requestAnimationFrame(animate);
      }
    },
    
    get isRunning() {
      return isRunning;
    },
    
    get isPaused() {
      return isPaused;
    }
  };
}

/**
 * 计算两点之间的距离
 * @param {Object} point1 起始点 {x, y, z}
 * @param {Object} point2 结束点 {x, y, z}
 * @returns {number} 距离
 */
export function calculateDistance(point1, point2) {
  const dx = point2.x - point1.x;
  const dy = point2.y - point1.y;
  const dz = point2.z - point1.z;
  return Math.sqrt(dx * dx + dy * dy + dz * dz);
}

/**
 * 线性插值
 * @param {number} start 起始值
 * @param {number} end 结束值
 * @param {number} t 插值参数 (0-1)
 * @returns {number} 插值结果
 */
export function lerp(start, end, t) {
  return start + (end - start) * t;
}

/**
 * 向量插值
 * @param {Object} start 起始向量 {x, y, z}
 * @param {Object} end 结束向量 {x, y, z}
 * @param {number} t 插值参数 (0-1)
 * @returns {Object} 插值结果向量
 */
export function lerpVector(start, end, t) {
  return {
    x: lerp(start.x, end.x, t),
    y: lerp(start.y, end.y, t),
    z: lerp(start.z, end.z, t)
  };
}

/**
 * 限制数值在指定范围内
 * @param {number} value 要限制的值
 * @param {number} min 最小值
 * @param {number} max 最大值
 * @returns {number} 限制后的值
 */
export function clamp(value, min, max) {
  return Math.min(Math.max(value, min), max);
}

/**
 * 将数值从一个范围映射到另一个范围
 * @param {number} value 要映射的值
 * @param {number} inMin 输入范围最小值
 * @param {number} inMax 输入范围最大值
 * @param {number} outMin 输出范围最小值
 * @param {number} outMax 输出范围最大值
 * @returns {number} 映射后的值
 */
export function mapRange(value, inMin, inMax, outMin, outMax) {
  return (value - inMin) * (outMax - outMin) / (inMax - inMin) + outMin;
}

/**
 * 生成随机数
 * @param {number} min 最小值
 * @param {number} max 最大值
 * @returns {number} 随机数
 */
export function random(min = 0, max = 1) {
  return Math.random() * (max - min) + min;
}

/**
 * 生成随机整数
 * @param {number} min 最小值
 * @param {number} max 最大值
 * @returns {number} 随机整数
 */
export function randomInt(min, max) {
  return Math.floor(random(min, max + 1));
}
