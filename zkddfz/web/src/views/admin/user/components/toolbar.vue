<template>
  <div class="flex gap-2">
    <a-button @click="refresh">刷新</a-button>

    <a-button @click="create">新建</a-button>

    <div class="flex-1" />

    <a-input class="max-w-80" v-model:value="quickFilterValue" placeholder="表内筛选" allow-clear />
  </div>
</template>

<script setup lang="ts">
import { emitter } from '@/emitter';
import { useQueryClient } from '@tanstack/vue-query';
import { message } from 'ant-design-vue';
import { ref, watch } from 'vue';

const props = defineProps({
  tableId: {
    type: String,
    required: true,
  },
  upsertDrawerId: {
    type: String,
    required: true,
  },
});

const emits = defineEmits(['filter']);

const queryClient = useQueryClient();

const quickFilterValue = ref('');
watch([quickFilterValue], () => {
  emits('filter', quickFilterValue.value);
});

const refresh = async () => {
  await queryClient.invalidateQueries({ queryKey: [props.tableId] });
  message.success('刷新成功');
};

const create = () => {
  emitter.emit('app:drawer', { id: props.upsertDrawerId });
};
</script>
