<template>
  <a-menu
    :items="items"
    :theme="isDark ? 'dark' : 'light'"
    @click="handleClick"
    class="w-64 dark:bg-neutral-900"
    style="min-height: calc(100vh - 48px)"
    v-model:selectedKeys="selectedKeys"
  />
</template>

<script setup lang="ts">
import { ContainerOutlined, UserOutlined } from '@ant-design/icons-vue';
import { useDark } from '@vueuse/core';
import type { ItemType, MenuProps } from 'ant-design-vue';
import { h, ref, watchEffect } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const isDark = useDark();

const route = useRoute();

const router = useRouter();

const selectedKeys = ref<string[]>();
watchEffect(() => {
  selectedKeys.value = [route.name] as string[];
});

const items = ref<ItemType[]>([
  { label: '用户', key: 'admin/user', icon: () => h(UserOutlined) },
  { label: '操作记录', key: 'admin/activity', icon: () => h(ContainerOutlined) },
]);

const handleClick: MenuProps['onClick'] = event => {
  router.push({ name: event.key as string });
};
</script>
