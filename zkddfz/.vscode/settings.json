{"editor.formatOnSave": false, "editor.codeActionsOnSave": ["source.organizeImports"], "files.eol": "\n", "files.insertFinalNewline": true, "git.enableSmartCommit": true, "git.autofetch": true, "[bat]": {"files.eol": "\r\n"}, "[java]": {"editor.tabSize": 2, "editor.insertSpaces": true, "editor.defaultFormatter": "redhat.java"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[powershell]": {"files.eol": "\r\n"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "java.compile.nullAnalysis.mode": "automatic"}