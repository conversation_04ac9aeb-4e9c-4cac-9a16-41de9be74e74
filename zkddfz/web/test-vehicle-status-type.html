<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车辆状态类型字段测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-case {
            border: 1px solid #ddd;
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-title {
            font-weight: bold;
            color: #007bff;
            font-size: 16px;
            margin-bottom: 10px;
        }
        .code-block {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .expected-result {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .mapping-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .mapping-table th, .mapping-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .mapping-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .priority {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        h2 {
            color: #555;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚗 车辆状态类型字段测试</h1>
        
        <h2>📋 功能说明</h2>
        <p>在 <code>getAllVehicleStatus()</code> 函数中添加了 <code>type</code> 字段，该字段包含车辆类型代码。</p>
        
        <h2>🔄 类型代码映射表</h2>
        <table class="mapping-table">
            <thead>
                <tr>
                    <th>类型代码</th>
                    <th>车辆名称</th>
                    <th>英文名称</th>
                    <th>deviceType ID</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>MBC</strong></td>
                    <td>移动转载机</td>
                    <td>Mobile Transfer Machine</td>
                    <td>mobile_loader</td>
                </tr>
                <tr>
                    <td><strong>BBC</strong></td>
                    <td>桥式转载机</td>
                    <td>Bridge Transfer Machine</td>
                    <td>bridge_loader</td>
                </tr>
                <tr>
                    <td><strong>TBC</strong></td>
                    <td>中继转载机</td>
                    <td>Relay Transfer Machine</td>
                    <td>relay_loader</td>
                </tr>
                <tr>
                    <td><strong>BWE</strong></td>
                    <td>3500T斗轮挖掘机</td>
                    <td>3500T Bucket Wheel Excavator</td>
                    <td>excavator_3500t</td>
                </tr>
                <tr>
                    <td><strong>RSC</strong></td>
                    <td>大型布料机</td>
                    <td>Large Spreader</td>
                    <td>large_spreader</td>
                </tr>
                <tr style="background-color: #e8f5e8;">
                    <td><strong>PCC</strong></td>
                    <td>移动供电车</td>
                    <td>Mobile Power Supply Vehicle</td>
                    <td>mobile_power_vehicle</td>
                </tr>
                <tr>
                    <td><strong>RSCM</strong></td>
                    <td>扇形布料机</td>
                    <td>Fan-shaped Spreader</td>
                    <td>fan_spreader</td>
                </tr>
                <tr>
                    <td><strong>BWEM</strong></td>
                    <td>1500T斗轮挖掘机</td>
                    <td>1500T Bucket Wheel Excavator</td>
                    <td>excavator_1500t</td>
                </tr>
                <tr style="background-color: #e8f5e8;">
                    <td><strong>MDF</strong></td>
                    <td>移动分料漏斗</td>
                    <td>Mobile Distribution Funnel</td>
                    <td>mobile_distribution_funnel</td>
                </tr>
            </tbody>
        </table>

        <h2>🔍 类型识别优先级</h2>
        <div class="priority">
            <p><strong>getVehicleTypeCode() 函数按以下优先级识别车辆类型：</strong></p>
            <ol>
                <li><strong>model.type</strong> - 直接使用模型的type字段（最高优先级）</li>
                <li><strong>设备ID格式</strong> - 从新格式ID中提取（如：PCC_12345 → PCC）</li>
                <li><strong>deviceType字段</strong> - 使用deviceType映射表</li>
                <li><strong>模型路径</strong> - 根据3D模型文件路径映射</li>
                <li><strong>模型名称</strong> - 根据模型名称关键词匹配</li>
                <li><strong>默认值</strong> - 如果都无法识别，默认返回 'MBC'</li>
            </ol>
        </div>

        <h2>🧪 测试用例</h2>

        <div class="test-case">
            <div class="test-title">测试用例 1: 新格式设备ID</div>
            <div class="code-block">
模型数据:
{
  "id": "PCC_12345",
  "name": "移动供电车",
  "currentPosition": { "x": 100, "y": 0, "z": 200 },
  "battery": 85,
  "consume": 15
}
            </div>
            <div class="expected-result">
                <strong>期望结果:</strong> type = "PCC"<br>
                <strong>识别方式:</strong> 从设备ID中提取类型代码
            </div>
        </div>

        <div class="test-case">
            <div class="test-title">测试用例 2: deviceType字段</div>
            <div class="code-block">
模型数据:
{
  "id": "device_67890",
  "name": "桥式转载机",
  "deviceType": "bridge_loader",
  "currentPosition": { "x": 150, "y": 0, "z": 300 },
  "battery": 92,
  "consume": 8
}
            </div>
            <div class="expected-result">
                <strong>期望结果:</strong> type = "BBC"<br>
                <strong>识别方式:</strong> 通过deviceType字段映射
            </div>
        </div>

        <div class="test-case">
            <div class="test-title">测试用例 3: 模型路径映射</div>
            <div class="code-block">
模型数据:
{
  "id": "device_11111",
  "name": "挖掘机设备",
  "path": "/models/new/3500T斗轮挖掘机.glb",
  "currentPosition": { "x": 200, "y": 0, "z": 400 },
  "battery": 78,
  "consume": 22
}
            </div>
            <div class="expected-result">
                <strong>期望结果:</strong> type = "BWE"<br>
                <strong>识别方式:</strong> 通过模型路径映射
            </div>
        </div>

        <div class="test-case">
            <div class="test-title">测试用例 4: 模型名称匹配</div>
            <div class="code-block">
模型数据:
{
  "id": "device_22222",
  "name": "扇形布料机设备A",
  "currentPosition": { "x": 250, "y": 0, "z": 500 },
  "battery": 95,
  "consume": 5
}
            </div>
            <div class="expected-result">
                <strong>期望结果:</strong> type = "RSCM"<br>
                <strong>识别方式:</strong> 通过模型名称关键词匹配
            </div>
        </div>

        <h2>📡 WebSocket 数据结构示例</h2>
        <div class="code-block">
// getAllVehicleStatus() 返回的数据结构
[
  {
    "device_id": "PCC_12345",
    "position": {
      "x": 100,
      "y": 0,
      "z": 200
    },
    "rotation": {
      "x": 0,
      "y": 90,
      "z": 0
    },
    "duration": 0,
    "power": 85,
    "consume": 15,
    "type": "PCC"  // ← 新增的类型字段
  },
  {
    "device_id": "BBC_67890",
    "position": {
      "x": 150,
      "y": 0,
      "z": 300
    },
    "rotation": {
      "x": 0,
      "y": 45,
      "z": 0
    },
    "duration": 0,
    "power": 92,
    "consume": 8,
    "type": "BBC"  // ← 新增的类型字段
  }
]
        </div>

        <h2>✅ 验证步骤</h2>
        <ol>
            <li>打开路线仿真页面</li>
            <li>添加不同类型的车辆设备</li>
            <li>启动WebSocket连接</li>
            <li>查看浏览器控制台中的车辆状态信息</li>
            <li>确认每个车辆状态对象都包含正确的 <code>type</code> 字段</li>
            <li>验证类型代码与车辆类型的对应关系</li>
        </ol>

        <h2>🔧 实现细节</h2>
        <ul>
            <li>✅ 在 <code>getAllVehicleStatus()</code> 函数中添加了 <code>type</code> 字段</li>
            <li>✅ 创建了 <code>getVehicleTypeCode()</code> 函数进行类型识别</li>
            <li>✅ 支持多种识别方式，确保兼容性</li>
            <li>✅ 包含所有9种车辆类型的完整映射</li>
            <li>✅ 提供默认值防止未知类型</li>
        </ul>
    </div>
</body>
</html>
