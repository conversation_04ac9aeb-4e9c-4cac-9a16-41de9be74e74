package com.baosight.api.udp;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

/**
 * UDP数据接收器
 * 用于接收UDP数据并转发给WebSocket客户端
 */
@Component
public class UdpDataReceiver {

    private static final Logger logger = LoggerFactory.getLogger(UdpDataReceiver.class);

    @Value("${udp.server.port:9090}")
    private int udpPort;

    @Value("${udp.server.buffer.size:1024}")
    private int bufferSize;

    private DatagramSocket socket;
    private final AtomicBoolean running = new AtomicBoolean(false);
    private final ObjectMapper objectMapper = new ObjectMapper();
    private Consumer<String> dataHandler;

    /**
     * 启动UDP服务端
     * @param dataHandler 数据处理回调函数
     */
    public CompletableFuture<Void> startServer(Consumer<String> dataHandler) {
        if (running.get()) {
            logger.warn("⚠️ UDP服务端已经在运行中，端口: {}", udpPort);
            return CompletableFuture.completedFuture(null);
        }

        this.dataHandler = dataHandler;

        return CompletableFuture.runAsync(() -> {
            try {
                socket = new DatagramSocket(udpPort);
                running.set(true);
                logger.info("🚀 UDP服务端启动成功，监听端口: {}", udpPort);

                byte[] buffer = new byte[bufferSize];
                DatagramPacket packet = new DatagramPacket(buffer, buffer.length);

                while (running.get() && !socket.isClosed()) {
                    try {
                        // 接收UDP数据包
                        socket.receive(packet);
                        
                        // 解析接收到的数据
                        String receivedData = new String(packet.getData(), 0, packet.getLength(), StandardCharsets.UTF_8);
                        InetAddress clientAddress = packet.getAddress();
                        int clientPort = packet.getPort();

                        logger.debug("📨 收到UDP数据 来自 {}:{}, 长度: {} 字节", 
                            clientAddress.getHostAddress(), clientPort, packet.getLength());
                        logger.debug("📄 数据内容: {}", receivedData);

                        // 处理接收到的数据
                        if (dataHandler != null) {
                            dataHandler.accept(receivedData);
                        }

                        // 重置数据包以便下次接收
                        packet.setLength(buffer.length);

                    } catch (SocketTimeoutException e) {
                        // 超时是正常的，继续循环
                        continue;
                    } catch (SocketException e) {
                        if (running.get()) {
                            logger.error("UDP Socket异常: {}", e.getMessage());
                        }
                        break;
                    } catch (Exception e) {
                        logger.error("处理UDP数据时发生错误: {}", e.getMessage(), e);
                    }
                }

            } catch (SocketException e) {
                logger.error("无法启动UDP服务端，端口 {} 可能被占用: {}", udpPort, e.getMessage());
                running.set(false);
            } catch (Exception e) {
                logger.error("UDP服务端启动失败: {}", e.getMessage(), e);
                running.set(false);
            }
        });
    }

    /**
     * 停止UDP服务端
     */
    public void stopServer() {
        if (!running.get()) {
            logger.debug("UDP服务端未运行，无需停止");
            return;
        }

        logger.info("🛑 正在停止UDP服务端...");
        running.set(false);

        if (socket != null && !socket.isClosed()) {
            socket.close();
            logger.info("✅ UDP服务端已停止，端口: {}", udpPort);
        }

        dataHandler = null;
    }

    /**
     * 检查UDP服务端是否正在运行
     */
    public boolean isRunning() {
        return running.get() && socket != null && !socket.isClosed();
    }

    /**
     * 获取UDP端口
     */
    public int getUdpPort() {
        return udpPort;
    }

    /**
     * 发送UDP响应（可选功能）
     */
    public void sendResponse(String response, InetAddress clientAddress, int clientPort) {
        if (socket != null && !socket.isClosed()) {
            try {
                byte[] responseData = response.getBytes(StandardCharsets.UTF_8);
                DatagramPacket responsePacket = new DatagramPacket(
                    responseData, responseData.length, clientAddress, clientPort);
                socket.send(responsePacket);
                logger.debug("📤 发送UDP响应到 {}:{}, 长度: {} 字节", 
                    clientAddress.getHostAddress(), clientPort, responseData.length);
            } catch (Exception e) {
                logger.error("发送UDP响应失败: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 验证JSON格式
     */
    private boolean isValidJson(String data) {
        try {
            objectMapper.readTree(data);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
