package com.baosight.api.feature.admin.route.get_user_roles;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import com.baosight.api.db.user.UserRepository;
import com.baosight.api.db.user_role.UserRoleEntity;
import com.baosight.api.db.user_role.UserRoleRepository;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class GetUserRolesService {
  private final UserRepository userRepository;

  private final UserRoleRepository userRoleRepository;

  private void checkUserExists(GetUserRolesRequestBody requestBody) {
    userRepository.findByUsername(requestBody.getUsername())
        .orElseThrow(
            () -> new ResponseStatusException(HttpStatus.UNPROCESSABLE_ENTITY, "用户不存在"));
  }

  public List<UserRoleEntity> getUserRoles(GetUserRolesRequestBody requestBody) {
    checkUserExists(requestBody);

    return userRoleRepository.findAllByUsername(requestBody.getUsername());
  }
}
