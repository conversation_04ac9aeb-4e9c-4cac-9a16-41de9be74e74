<template>
  <div class="w-full p-2 space-y-2">
    <search :full-data="fullData" ref="search" @search="handleSearch" />

    <a-divider />

    <toolbar ref="toolbar" :table-id="tableId" :upsert-drawer-id="upsertDrawerId" @filter="quickFilter" />

    <ag-grid
      :id="tableId"
      :column-defs="columnDefs"
      :get-context-menu-items="getContextMenuItems"
      :row-data="rowData"
      :style="{ height }"
      @first-data-rendered="gridApi?.sizeColumnsToFit()"
      @grid-ready="setGridApi"
    />

    <upsert-drawer :id="upsertDrawerId" :table-id="tableId" />
  </div>
</template>

<script setup lang="ts">
import type { ConditionMap } from '@/common/types';
import { AgGrid, useGridApi } from '@/components/ag-grid';
import { useQuery } from '@tanstack/vue-query';
import { useElementSize } from '@vueuse/core';
import { computed, ref } from 'vue';
import Search from './components/search.vue';
import Toolbar from './components/toolbar.vue';
import UpsertDrawer from './components/upsert.vue';
import { useGetContextMenuItems } from './libraries/composables';
import { tableId, upsertDrawerId } from './libraries/constants';
import { createColumnDefs, getUsers } from './libraries/services';

const toolbar = ref();

const search = ref();

const getContextMenuItems = useGetContextMenuItems();

const { height: toolbarHeight } = useElementSize(toolbar);

const { height: searchHeight } = useElementSize(search);

const height = computed(() => `calc(100vh - ${toolbarHeight.value}px - ${searchHeight.value}px - 92px)`);

const [gridApi, setGridApi] = useGridApi();

const quickFilter = (value: string) => {
  gridApi.value?.setGridOption('quickFilterText', value);
};

const columnDefs = createColumnDefs();

const conditionMap = ref<ConditionMap>();

const handleSearch = (payload: ConditionMap) => {
  conditionMap.value = payload;
};

const { data: fullData } = useQuery({
  queryKey: [tableId],
  queryFn: () => getUsers(),
});

const { data: rowData } = useQuery({
  queryKey: [tableId, conditionMap],
  queryFn: () => getUsers(conditionMap),
});
</script>
