package com.baosight.api.feature.admin.route.delete_user;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baosight.api.constants.ApiPaths;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
public class DeleteUserController {
  private final DeleteUserService userService;

  @PostMapping(ApiPaths.Admin.DELETE_USER)
  public void handleRequest(@Valid @RequestBody DeleteUserRequestBody requestBody) {
    userService.deleteUser(requestBody);
  }
}
