<script setup lang="ts">
import { ref } from 'vue';
import ModelCard from './components/ModelCard.vue';

// 模型配置（使用地面坐标系：Y=0表示地面）
const models = ref([
  {
    id: 'car1',
    name: '1500T斗轮挖掘机',
    path: '/models/1500T斗轮挖掘机.glb',
    // 初始坐标和角度（模型的基准位置，加载时的默认位置）
    initialPosition: { x: -80, y: 0, z: -60 }, // 地面坐标系：Y=0表示放在地面上
    initialRotation: { x: 0, y: 45, z: 0 },
    // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
    currentPosition: { x: -80, y: 0, z: -60 },
    currentRotation: { x: 0, y: 45, z: 0 },
    scale: 0.01,
    defaultAnimation: 0,
    movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
    autoGroundPosition: true, // 自动地面定位
    material: {
      color: 0x0080ff,
    },
  },
  {
    id: 'car3',
    name: '3500T斗轮挖掘机',
    path: '/models/3500T斗轮挖掘机.glb',
    // 初始坐标和角度（模型的基准位置，加载时的默认位置）
    initialPosition: { x: 120, y: 0, z: -90 }, // 地面坐标系：Y=0表示放在地面上
    initialRotation: { x: 0, y: 180, z: 0 },
    // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
    currentPosition: { x: 120, y: 0, z: -90 },
    currentRotation: { x: 0, y: 180, z: 0 },
    scale: 0.01,
    defaultAnimation: 0,
    movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
    autoGroundPosition: true, // 自动地面定位
    material: {
      color: 0x0080ff,
    },
  },
  {
    id: 'car5',
    name: '扇形布料机',
    path: '/models/扇形布料机ok.glb',
    // 初始坐标和角度（模型的基准位置，加载时的默认位置）
    initialPosition: { x: -150, y: 0, z: 80 }, // 地面坐标系：Y=0表示放在地面上
    initialRotation: { x: 0, y: 270, z: 0 },
    // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
    currentPosition: { x: -150, y: 0, z: 80 },
    currentRotation: { x: 0, y: 270, z: 0 },
    scale: 0.01,
    defaultAnimation: 0,
    movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
    autoGroundPosition: true, // 自动地面定位
    material: {
      color: 0x0080ff,
    },
  },
  {
    id: 'car6',
    name: '中继转载机',
    path: '/models/中继转载机-B-ok.glb',
    // 初始坐标和角度（模型的基准位置，加载时的默认位置）
    initialPosition: { x: 0, y: 0, z: 0 }, // 地面坐标系：Y=0表示放在地面上
    initialRotation: { x: 0, y: 90, z: 0 },
    // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
    currentPosition: { x: 60, y: 0, z: 140 },
    currentRotation: { x: 0, y: 90, z: 0 },
    scale: 0.01,
    defaultAnimation: 0,
    movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
    autoGroundPosition: true, // 自动地面定位
    material: {
      color: 0x0080ff,
    },
  },
  {
    id: 'car7',
    name: '大型布料机',
    path: '/models/大型布料机ok-6.glb',
    // 初始坐标和角度（模型的基准位置，加载时的默认位置）
    initialPosition: { x: 0, y: 0, z: 0 }, // 地面坐标系：Y=0表示放在地面上
    initialRotation: { x: 0, y: 0, z: 0 },
    // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
    currentPosition: { x: 675, y: 0.01, z: -496 },
    currentRotation: { x: 0, y: -180, z: 0 },
    scale: 0.0088,
    receiveShadow: true,
    castShadow: true,
    defaultAnimation: 0,
    movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
    autoGroundPosition: true, // 自动地面定位
    material: {
      color: 0x0080ff,
    },
  },
  {
    id: 'car8',
    name: '桥式转载机',
    path: '/models/桥式转载机ok.glb',
    // 初始坐标和角度（模型的基准位置，加载时的默认位置）
    initialPosition: { x: 180, y: 0, z: 50 }, // 地面坐标系：Y=0表示放在地面上
    initialRotation: { x: 0, y: 135, z: 0 },
    // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
    currentPosition: { x: 180, y: 0, z: 50 },
    currentRotation: { x: 0, y: 135, z: 0 },
    scale: 0.005,
    defaultAnimation: 0,
    movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
    autoGroundPosition: true, // 自动地面定位
    material: {
      color: 0x0080ff,
    },
  },
  {
    id: 'car9',
    name: '移动供电车',
    path: '/models/移动供电车ok.glb',
    // 初始坐标和角度（模型的基准位置，加载时的默认位置）
    initialPosition: { x: -100, y: 0, z: -30 }, // 地面坐标系：Y=0表示放在地面上
    initialRotation: { x: 0, y: 60, z: 0 },
    // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
    currentPosition: { x: -100, y: 0, z: -30 },
    currentRotation: { x: 0, y: 60, z: 0 },
    scale: 0.01,
    defaultAnimation: 0,
    movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
    autoGroundPosition: true, // 自动地面定位
    material: {
      color: 0x0080ff,
    },
  },
  {
    id: 'car10',
    name: '移动分料漏斗',
    path: '/models/移动分料漏斗ok.glb',
    // 初始坐标和角度（模型的基准位置，加载时的默认位置）
    initialPosition: { x: 30, y: 0, z: -160 }, // 地面坐标系：Y=0表示放在地面上
    initialRotation: { x: 0, y: 225, z: 0 },
    // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
    currentPosition: { x: 30, y: 0, z: -160 },
    currentRotation: { x: 0, y: 225, z: 0 },
    scale: 0.01,
    defaultAnimation: 0,
    movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
    autoGroundPosition: true, // 自动地面定位
    material: {
      color: 0x0080ff,
    },
  },
  {
    id: 'car11',
    name: '移动受料漏斗',
    path: '/models/移动受料漏斗ok.glb',
    // 初始坐标和角度（模型的基准位置，加载时的默认位置）
    initialPosition: { x: -70, y: 0, z: 110 }, // 地面坐标系：Y=0表示放在地面上
    initialRotation: { x: 0, y: 150, z: 0 },
    // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
    currentPosition: { x: -70, y: 0, z: 110 },
    currentRotation: { x: 0, y: 150, z: 0 },
    scale: 0.01,
    defaultAnimation: 0,
    movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
    autoGroundPosition: true, // 自动地面定位
    material: {
      color: 0x0080ff,
    },
  },
  {
    id: 'car12',
    name: '移动转载机',
    path: '/models/移动转载机ok.glb',
    // 初始坐标和角度（模型的基准位置，加载时的默认位置）
    initialPosition: { x: 140, y: 0, z: 20 }, // 地面坐标系：Y=0表示放在地面上
    initialRotation: { x: 0, y: 300, z: 0 },
    // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
    currentPosition: { x: 140, y: 0, z: 20 },
    currentRotation: { x: 0, y: 300, z: 0 },
    scale: 0.01,
    defaultAnimation: 0,
    movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
    autoGroundPosition: true, // 自动地面定位
    material: {
      color: 0x0080ff,
    },
  },
]);

const loadingStatus = ref<Record<string, 'loaded' | 'loading' | 'error'>>({
  car1: 'loaded',
  drone2: 'loading',
  robot3: 'error',
});

const firstPersonView = ref<{ targetModelId: string | null }>({ targetModelId: null });

const toggleFirstPersonView = (id: string) => {
  firstPersonView.value.targetModelId = firstPersonView.value.targetModelId === id ? null : id;
};

const removeModel = (id: string) => {
  models.value = models.value.filter(model => model.id !== id);
  const newStatus = { ...loadingStatus.value };
  delete newStatus[id];
  loadingStatus.value = newStatus;

  if (firstPersonView.value.targetModelId === id) {
    firstPersonView.value.targetModelId = null;
  }
};
</script>

<template>
  <div
    class="relative bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 text-white overflow-hidden"
    :style="{ height: 'calc(100vh - 48px)' }"
  >
    <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-5">
      <ModelCard
        v-for="model in models"
        :key="model.id"
        :model="model"
        :loadingStatus="loadingStatus"
        :firstPersonView="firstPersonView"
        @toggleFirstPersonView="toggleFirstPersonView"
        @removeModel="removeModel"
      />
    </div>
  </div>
</template>
