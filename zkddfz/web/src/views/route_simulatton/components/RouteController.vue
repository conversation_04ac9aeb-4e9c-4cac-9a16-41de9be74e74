<template>
  <div class="route-controller">
    <div class="panel-section">
      <div class="panel-header" @click="togglePanel">
        <h3>路线模拟控制</h3>
        <div class="panel-toggle">
          <svg
            :class="['toggle-icon', { expanded: isExpanded }]"
            viewBox="0 0 24 24"
            width="20"
            height="20"
          >
            <path d="M7 10l5 5 5-5z" fill="currentColor"/>
          </svg>
        </div>
      </div>

      <div v-show="isExpanded" class="panel-content">
      
      <!-- 数据源选择 -->
      <div class="control-group">
        <label>数据源:</label>
        <div class="data-source-tabs">
          <button 
            :class="['tab-btn', { active: dataSource === 'json' }]"
            @click="setDataSource('json')"
          >
            JSON数组
          </button>
          <button 
            :class="['tab-btn', { active: dataSource === 'websocket' }]"
            @click="setDataSource('websocket')"
          >
            WebSocket实时
          </button>
        </div>
      </div>
      
      <!-- JSON数据输入 -->
      <div v-if="dataSource === 'json'" class="json-input-section">
        <div class="control-group">
          <label>路线数据 (JSON格式):</label>
          <textarea 
            v-model="jsonInput" 
            class="json-textarea"
            placeholder="输入JSON格式的路线数据..."
            rows="8"
          ></textarea>
          <div class="json-actions">
            <button @click="loadSampleData" class="btn secondary">转弯模拟</button>
            <button @click="loadCityRouteData" class="btn secondary">排布模拟</button>
            <button @click="loadCircularRouteData" class="btn secondary">能耗模拟</button>
            <button @click="loadForwardBackwardRouteData" class="btn secondary">感知模拟</button>
            <button @click="loadJsonData" class="btn primary">加载路线数据</button>
          </div>
        </div>
      </div>
      
      <!-- WebSocket配置 -->
      <div v-if="dataSource === 'websocket'" class="websocket-section">
        <div class="control-group">
          <label>WebSocket URL:</label>
          <input 
            v-model="websocketUrl" 
            type="text" 
            class="url-input"
            placeholder="ws://localhost:8080/route"
          />
          <button 
            @click="toggleWebSocket" 
            :class="['btn', websocketConnected ? 'danger' : 'primary']"
          >
            {{ websocketConnected ? '断开连接' : '连接WebSocket' }}
          </button>
        </div>
        <div class="connection-status">
          <span :class="['status-indicator', websocketConnected ? 'connected' : 'disconnected']">
            {{ websocketConnected ? '已连接' : '未连接' }}
          </span>
        </div>
      </div>
      
      <!-- 仿真控制 -->
      <div class="playback-controls">
        <div class="control-group">
          <label>仿真控制:</label>
          <div class="control-buttons">
            <button 
              @click="startRoute" 
              :disabled="!canStart"
              class="btn primary"
            >
              开始
            </button>
            <button 
              @click="pauseRoute" 
              :disabled="!simulator.isRunning || simulator.isPaused"
              class="btn secondary"
            >
              暂停
            </button>
            <button 
              @click="resumeRoute" 
              :disabled="!simulator.isPaused"
              class="btn secondary"
            >
              继续
            </button>
            <button 
              @click="stopRoute" 
              :disabled="!simulator.isRunning"
              class="btn danger"
            >
              停止
            </button>
          </div>
        </div>
        
        <!-- 进度显示 -->
        <div class="progress-section" v-if="routeStatus.totalPoints > 0">
          <div class="progress-info">
            <span>进度: {{ routeStatus.currentIndex + 1 }} / {{ routeStatus.totalPoints }}</span>
            <span>{{ Math.round(routeStatus.progress * 100) }}%</span>
          </div>
          <div class="progress-bar">
            <div 
              class="progress-fill" 
              :style="{ width: routeStatus.progress * 100 + '%' }"
            ></div>
          </div>
        </div>
      </div>
      
      <!-- 配置选项 -->
      <div class="config-section">
        <div class="control-group">
          <label>仿真速度:</label>
          <input 
            v-model.number="playbackSpeed" 
            type="range" 
            min="0.1" 
            max="5" 
            step="0.1"
            class="speed-slider"
            @input="updatePlaybackSpeed"
          />
          <span class="speed-value">{{ playbackSpeed }}x</span>
        </div>
        
        <div class="control-group">
          <label>插值模式:</label>
          <select v-model="interpolationMode" @change="updateInterpolationMode">
            <option value="linear">线性</option>
            <option value="smooth">平滑</option>
          </select>
        </div>
        
        <div class="control-group">
          <div class="checkbox-group">
            <input 
              type="checkbox" 
              id="loop-playback" 
              v-model="loopPlayback"
              @change="updateLoopMode"
            />
            <label for="loop-playback">循环仿真</label>
          </div>
          
          <div class="checkbox-group">
            <input 
              type="checkbox" 
              id="smooth-rotation" 
              v-model="smoothRotation"
              @change="updateSmoothRotation"
            />
            <label for="smooth-rotation">平滑旋转</label>
          </div>
        </div>
      </div>
      
      <!-- 状态信息 -->
      <div class="status-section">
        <div class="status-item">
          <strong>当前状态:</strong> 
          <span :class="['status-text', getStatusClass()]">
            {{ getStatusText() }}
          </span>
        </div>
        <div class="status-item" v-if="currentPosition">
          <strong>当前位置:</strong> 
          X: {{ currentPosition.x.toFixed(2) }}, 
          Y: {{ currentPosition.y.toFixed(2) }}, 
          Z: {{ currentPosition.z.toFixed(2) }}
        </div>
        <div class="status-item" v-if="currentRotation">
          <strong>当前角度:</strong> 
          X: {{ Math.round(THREE.MathUtils.radToDeg(currentRotation.x)) }}°, 
          Y: {{ Math.round(THREE.MathUtils.radToDeg(currentRotation.y)) }}°, 
          Z: {{ Math.round(THREE.MathUtils.radToDeg(currentRotation.z)) }}°
        </div>
      </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, onBeforeUnmount } from 'vue';
import * as THREE from 'three';
import { RouteSimulator } from '../utils/RouteSimulator.js';

const props = defineProps({
  model: {
    type: Object,
    default: null
  },
  modelComponent: {
    type: Object,
    default: null
  },
  sceneManagerRef: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['status-change', 'position-update']);

// 数据源
const dataSource = ref('json');
const jsonInput = ref('');
const websocketUrl = ref('ws://localhost:8080/route');
const websocketConnected = ref(false);

// 路线模拟器
const simulator = reactive(new RouteSimulator({
  interpolationMode: 'linear',
  speed: 1.0,
  loop: false,
  smoothRotation: true
}));

// 仿真控制
const playbackSpeed = ref(1.0);
const interpolationMode = ref('linear');
const loopPlayback = ref(false);
const smoothRotation = ref(true);

// 状态
const routeStatus = reactive({
  isRunning: false,
  isPaused: false,
  currentIndex: 0,
  totalPoints: 0,
  progress: 0
});

const currentPosition = ref(null);
const currentRotation = ref(null);

// 面板展开/关闭状态
const isExpanded = ref(false);

// 计算属性
const canStart = computed(() => {
  const hasModel = !!simulator.model;
  const hasRouteData = (dataSource.value === 'json' && simulator.routeData.length > 0) ||
                       (dataSource.value === 'websocket' && websocketConnected.value);
  return hasModel && hasRouteData;
});

// 汽车模拟数据（400x400地图范围，平滑行驶路径）
const sampleRouteData2 = [
  {
    timestamp: Date.now(),
    position: { x: 100, y: 0, z: 55.5 },
    rotation: { x: 0, y: 90, z: 0 },    // 面向北方
    duration: 1000
  },
  {
    timestamp: Date.now() + 1000,
    position: { x: 100, y: 0, z: 98.5 },
    rotation: { x: 0, y: 90, z: 0 },    // 继续向北
    duration: 3000
  },
  {
    timestamp: Date.now() + 4000,
    position: { x: 85, y: 0, z: 98.5},
    rotation: { x: 0, y: 90, z: 0 },   // 东北方向
    duration: 2500
  },
  {
    timestamp: Date.now() + 6500,
    position: { x: 97.8898569830913, y: 0, z: 95.6423875046178 },
    rotation: { x: 0, y: 65, z: 0 },   // 面向东方
    duration: 3000
  },
  {
    timestamp: Date.now() + 9500,
    position: { x: 101.059493946147, y: 0, z: 102.439695907393 },
    rotation: { x: 0, y: 65, z: 0 },  // 东南方向
    duration: 2000
  },
  {
    timestamp: Date.now() + 11500,
    position: { x: 105.663718271762, y: 0, z: 99.7814457535891 },
    rotation: { x: 0, y: 55, z: 0 },  // 面向南方
    duration: 4000
  },
  {
    timestamp: Date.now() + 15500,
    position: { x: 102.591898105678, y: 0, z: 101.932357389906 },
    rotation: { x: 0, y: 55, z: 0 },  // 西南方向
    duration: 3500
  },
  {
    timestamp: Date.now() + 19000,
    position: { x: 106.664573623161, y: 0, z: 98.514976865281 },
    rotation: { x: 0, y: 45, z: 0 },  // 面向西方
    duration: 4000
  },
  {
    timestamp: Date.now() + 23000,
    position: { x: 105.780690146678, y: 0, z: 99.3988603417642 },
    rotation: { x: 0, y: 45, z: 0 },  // 西北方向
    duration: 3000
  },
  {
    timestamp: Date.now() + 26000,
    position: { x: 111.083991005577, y: 0, z: 104.702161200663 },
    rotation: { x: 0, y: 45, z: 0 },    // 面向北方
    duration: 3500
  },
  {
    timestamp: Date.now() + 29500,
    position: { x: 107.548457099644, y: 0, z: 108.237695106596 },
    rotation: { x: 0, y: 45, z: 0 },   // 东北方向
    duration: 2500
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 110.965837624269, y: 0, z: 104.165019589113 },
    rotation: { x: 0, y: 35, z: 0 },   // 面向东方停车
    duration: 3000
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 108.097955442514, y: 0, z: 108.260779810558 },
    rotation: { x: 0, y: 35, z: 0 },   // 面向东方停车
    duration: 3000
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 110.965837624269, y: 0, z: 104.165019589113 },
    rotation: { x: 0, y: 35, z: 0 },   // 面向东方停车
    duration: 3000
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 110.289517079445, y: 0, z: 104.613408743907 },
    rotation: { x: 0, y: 27, z: 0 },   // 面向东方停车
    duration: 3000
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 105.182123957375, y: 0, z: 114.637232141027 },
    rotation: { x: 0, y: 27, z: 0 },   // 面向东方停车
    duration: 3000
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 107.839926792011, y: 0, z: 107.131828468912 },
    rotation: { x: 0, y: 12, z: 0 },   // 面向东方停车
    duration: 3000
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 105.760809883833, y: 0, z: 116.91330447625 },
    rotation: { x: 0, y: 12, z: 0 },   // 面向东方停车
    duration: 3000
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 106.427308061452, y: 0, z: 110.571997906308 },
    rotation: { x: 0, y: 0, z: 0 },   // 面向东方停车
    duration: 3000
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 106.427308061452, y: 0, z: 129.321997906308 },
    rotation: { x: 0, y: 0, z: 0 },   // 面向东方停车
    duration: 3000
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 118.927308061452, y: 0, z: 129.321997906308 },
    rotation: { x: 0, y: 0, z: 0 },   // 面向东方停车
    duration: 3000
  }
];
const sampleRouteData = [
  {
    timestamp: Date.now(),
    position: { x: 100, y: 0, z: -120 },
    rotation: { x: 0, y: 90, z: 0 },    // 面向北方
    duration: 1000
  },
  {
    timestamp: Date.now(),
    position: { x: 100, y: 0, z: 25 },
    rotation: { x: 0, y: 90, z: 0 },    // 面向北方
    duration: 1000
  },
  {
    timestamp: Date.now() + 1000,
    position: { x: 100, y: 0, z: 68 },
    rotation: { x: 0, y: 90, z: 0 },    // 继续向北
    duration: 3000
  },
  {
    timestamp: Date.now() + 4000,
    position: { x: 85, y: 0, z: 68},
    rotation: { x: 0, y: 90, z: 0 },   // 东北方向
    duration: 2500
  },
  {
    timestamp: Date.now() + 6500,
    position: { x: 85, y: 0, z: 68 },
    rotation: { x: 0, y: 65, z: 0 },   // 面向东方
    duration: 3000
  },
  {
    timestamp: Date.now() + 9500,
    position: { x: 88.1696369630552, y: 0, z: 74.7973084027749 },
    rotation: { x: 0, y: 65, z: 0 },  // 东南方向
    duration: 2000
  },
  {
    timestamp: Date.now() + 11500,
    position: { x: 88.1696369630552, y: 0, z: 74.7973084027749 },
    rotation: { x: 0, y: 55, z: 0 },  // 面向南方
    duration: 4000
  },
  {
    timestamp: Date.now() + 15500,
    position: { x: 85.0978167969715, y: 0, z: 76.9482200390913 },
    rotation: { x: 0, y: 55, z: 0 },  // 西南方向
    duration: 3500
  },
  {
    timestamp: Date.now() + 19000,
    position: { x: 85.0978167969715, y: 0, z: 76.9482200390913 },
    rotation: { x: 0, y: 45, z: 0 },  // 面向西方
    duration: 4000
  },
  {
    timestamp: Date.now() + 23000,
    position: { x: 84.2139333204883, y: 0, z: 77.8321035155745 },
    rotation: { x: 0, y: 45, z: 0 },  // 西北方向
    duration: 3000
  },
  {
    timestamp: Date.now() + 26000,
    position: { x:89.5172341793874, y: 0, z: 83.1354043744736 },
    rotation: { x: 0, y: 45, z: 0 },    // 面向北方
    duration: 3500
  },
  {
    timestamp: Date.now() + 29500,
    position: { x: 85.9817002734547, y: 0, z: 86.6709382804063 },
    rotation: { x: 0, y: 45, z: 0 },   // 东北方向
    duration: 2500
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 85.9817002734547, y: 0, z: 86.6709382804063 },
    rotation: { x: 0, y: 35, z: 0 },   // 面向东方停车
    duration: 3000
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 83.1138180916995, y: 0, z: 90.7666985018513 },
    rotation: { x: 0, y: 35, z: 0 },   // 面向东方停车
    duration: 3000
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 83.1138180916995, y: 0, z: 90.7666985018513 },
    rotation: { x: 0, y: 27, z: 0 },   // 面向东方停车
    duration: 3000
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 78.0064249696296, y: 0, z: 100.79052189897 },
    rotation: { x: 0, y: 27, z: 0 },   // 面向东方停车
    duration: 3000
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 78.0064249696296, y: 0, z: 100.79052189897 },
    rotation: { x: 0, y: 12, z: 0 },   // 面向东方停车
    duration: 3000
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 75.927308061452, y: 0, z: 110.571997906308 },
    rotation: { x: 0, y: 12, z: 0 },   // 面向东方停车
    duration: 3000
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 75.927308061452, y: 0, z: 110.571997906308 },
    rotation: { x: 0, y: 0, z: 0 },   // 面向东方停车
    duration: 3000
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 75.927308061452, y: 0, z: 118.321997906308 },
    rotation: { x: 0, y: 0, z: 0 },   // 面向东方停车
    duration: 3000
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 118.427308061452, y: 0, z: 118.321997906308 },
    rotation: { x: 0, y: 0, z: 0 },   // 面向东方停车
    duration: 3000
  }
];
// 排布模拟数据（更复杂的汽车路径）
const cityRouteData = [
  // 起点：市中心
  { position: { x: 0, y: 0, z: 0 }, rotation: { x: 0, y: 0, z: 0 }, duration: 1000 },

  // 第一段：向北到商业区
  { position: { x: 0, y: 0, z: -60 }, rotation: { x: 0, y: 0, z: 0 }, duration: 2500 },
  { position: { x: 20, y: 0, z: -100 }, rotation: { x: 0, y: 30, z: 0 }, duration: 2000 },
  { position: { x: 60, y: 0, z: -120 }, rotation: { x: 0, y: 60, z: 0 }, duration: 2000 },
  { position: { x: 100, y: 0, z: -130 }, rotation: { x: 0, y: 80, z: 0 }, duration: 2000 },
  { position: { x: 140, y: 0, z: -125 }, rotation: { x: 0, y: 90, z: 0 }, duration: 2000 },

  // 第二段：东部工业区
  { position: { x: 170, y: 0, z: -100 }, rotation: { x: 0, y: 120, z: 0 }, duration: 1800 },
  { position: { x: 185, y: 0, z: -60 }, rotation: { x: 0, y: 150, z: 0 }, duration: 1800 },
  { position: { x: 190, y: 0, z: -20 }, rotation: { x: 0, y: 170, z: 0 }, duration: 1800 },
  { position: { x: 185, y: 0, z: 20 }, rotation: { x: 0, y: 180, z: 0 }, duration: 1800 },

  // 第三段：南部住宅区
  { position: { x: 170, y: 0, z: 60 }, rotation: { x: 0, y: 200, z: 0 }, duration: 2200 },
  { position: { x: 140, y: 0, z: 100 }, rotation: { x: 0, y: 220, z: 0 }, duration: 2200 },
  { position: { x: 100, y: 0, z: 130 }, rotation: { x: 0, y: 240, z: 0 }, duration: 2200 },
  { position: { x: 60, y: 0, z: 150 }, rotation: { x: 0, y: 260, z: 0 }, duration: 2200 },
  { position: { x: 20, y: 0, z: 160 }, rotation: { x: 0, y: 270, z: 0 }, duration: 2200 },

  // 第四段：西部公园区
  { position: { x: -20, y: 0, z: 155 }, rotation: { x: 0, y: 290, z: 0 }, duration: 2000 },
  { position: { x: -60, y: 0, z: 140 }, rotation: { x: 0, y: 310, z: 0 }, duration: 2000 },
  { position: { x: -100, y: 0, z: 115 }, rotation: { x: 0, y: 320, z: 0 }, duration: 2000 },
  { position: { x: -140, y: 0, z: 80 }, rotation: { x: 0, y: 330, z: 0 }, duration: 2000 },
  { position: { x: -170, y: 0, z: 40 }, rotation: { x: 0, y: 340, z: 0 }, duration: 2000 },
  { position: { x: -185, y: 0, z: 0 }, rotation: { x: 0, y: 350, z: 0 }, duration: 2000 },

  // 第五段：西北角到北部
  { position: { x: -180, y: 0, z: -40 }, rotation: { x: 0, y: 0, z: 0 }, duration: 2000 },
  { position: { x: -160, y: 0, z: -80 }, rotation: { x: 0, y: 20, z: 0 }, duration: 2000 },
  { position: { x: -130, y: 0, z: -110 }, rotation: { x: 0, y: 40, z: 0 }, duration: 2000 },
  { position: { x: -90, y: 0, z: -130 }, rotation: { x: 0, y: 60, z: 0 }, duration: 2000 },
  { position: { x: -50, y: 0, z: -140 }, rotation: { x: 0, y: 80, z: 0 }, duration: 2000 },

  // 最终回到市中心
  { position: { x: -20, y: 0, z: -120 }, rotation: { x: 0, y: 100, z: 0 }, duration: 1800 },
  { position: { x: 0, y: 0, z: -80 }, rotation: { x: 0, y: 120, z: 0 }, duration: 1800 },
  { position: { x: 10, y: 0, z: -40 }, rotation: { x: 0, y: 150, z: 0 }, duration: 1800 },
  { position: { x: 5, y: 0, z: -10 }, rotation: { x: 0, y: 170, z: 0 }, duration: 1500 },
  { position: { x: 0, y: 0, z: 0 }, rotation: { x: 0, y: 180, z: 0 }, duration: 2000 }
];

// 能耗模拟路线数据（大环形路径）
const circularRouteData = [
  // 起点：中心
  { position: { x: 0, y: 0, z: 0 }, rotation: { x: 0, y: 0, z: 0 }, duration: 1000 },

  // 第一段：向北移动到环形起点
  { position: { x: 0, y: 0, z: -100 }, rotation: { x: 0, y: 0, z: 0 }, duration: 2000 },

  // 开始环形路径 - 北部弧线（从北向东）
  { position: { x: 20, y: 0, z: -98 }, rotation: { x: 0, y: 15, z: 0 }, duration: 1500 },
  { position: { x: 39, y: 0, z: -92 }, rotation: { x: 0, y: 30, z: 0 }, duration: 1500 },
  { position: { x: 56, y: 0, z: -83 }, rotation: { x: 0, y: 45, z: 0 }, duration: 1500 },
  { position: { x: 71, y: 0, z: -71 }, rotation: { x: 0, y: 60, z: 0 }, duration: 1500 },
  { position: { x: 83, y: 0, z: -56 }, rotation: { x: 0, y: 75, z: 0 }, duration: 1500 },
  { position: { x: 92, y: 0, z: -39 }, rotation: { x: 0, y: 90, z: 0 }, duration: 1500 },

  // 东部弧线（从北向南）
  { position: { x: 98, y: 0, z: -20 }, rotation: { x: 0, y: 105, z: 0 }, duration: 1500 },
  { position: { x: 100, y: 0, z: 0 }, rotation: { x: 0, y: 120, z: 0 }, duration: 1500 },
  { position: { x: 98, y: 0, z: 20 }, rotation: { x: 0, y: 135, z: 0 }, duration: 1500 },
  { position: { x: 92, y: 0, z: 39 }, rotation: { x: 0, y: 150, z: 0 }, duration: 1500 },
  { position: { x: 83, y: 0, z: 56 }, rotation: { x: 0, y: 165, z: 0 }, duration: 1500 },
  { position: { x: 71, y: 0, z: 71 }, rotation: { x: 0, y: 180, z: 0 }, duration: 1500 },

  // 南部弧线（从东向西）
  { position: { x: 56, y: 0, z: 83 }, rotation: { x: 0, y: 195, z: 0 }, duration: 1500 },
  { position: { x: 39, y: 0, z: 92 }, rotation: { x: 0, y: 210, z: 0 }, duration: 1500 },
  { position: { x: 20, y: 0, z: 98 }, rotation: { x: 0, y: 225, z: 0 }, duration: 1500 },
  { position: { x: 0, y: 0, z: 100 }, rotation: { x: 0, y: 240, z: 0 }, duration: 1500 },
  { position: { x: -20, y: 0, z: 98 }, rotation: { x: 0, y: 255, z: 0 }, duration: 1500 },
  { position: { x: -39, y: 0, z: 92 }, rotation: { x: 0, y: 270, z: 0 }, duration: 1500 },

  // 西部弧线（从南向北）
  { position: { x: -56, y: 0, z: 83 }, rotation: { x: 0, y: 285, z: 0 }, duration: 1500 },
  { position: { x: -71, y: 0, z: 71 }, rotation: { x: 0, y: 300, z: 0 }, duration: 1500 },
  { position: { x: -83, y: 0, z: 56 }, rotation: { x: 0, y: 315, z: 0 }, duration: 1500 },
  { position: { x: -92, y: 0, z: 39 }, rotation: { x: 0, y: 330, z: 0 }, duration: 1500 },
  { position: { x: -98, y: 0, z: 20 }, rotation: { x: 0, y: 345, z: 0 }, duration: 1500 },
  { position: { x: -100, y: 0, z: 0 }, rotation: { x: 0, y: 0, z: 0 }, duration: 1500 },

  // 完成环形 - 西北部弧线（回到北部）
  { position: { x: -98, y: 0, z: -20 }, rotation: { x: 0, y: 15, z: 0 }, duration: 1500 },
  { position: { x: -92, y: 0, z: -39 }, rotation: { x: 0, y: 30, z: 0 }, duration: 1500 },
  { position: { x: -83, y: 0, z: -56 }, rotation: { x: 0, y: 45, z: 0 }, duration: 1500 },
  { position: { x: -71, y: 0, z: -71 }, rotation: { x: 0, y: 60, z: 0 }, duration: 1500 },
  { position: { x: -56, y: 0, z: -83 }, rotation: { x: 0, y: 75, z: 0 }, duration: 1500 },
  { position: { x: -39, y: 0, z: -92 }, rotation: { x: 0, y: 90, z: 0 }, duration: 1500 },
  { position: { x: -20, y: 0, z: -98 }, rotation: { x: 0, y: 105, z: 0 }, duration: 1500 },
  { position: { x: 0, y: 0, z: -100 }, rotation: { x: 0, y: 120, z: 0 }, duration: 1500 },

  // 回到中心
  { position: { x: 0, y: 0, z: -50 }, rotation: { x: 0, y: 180, z: 0 }, duration: 2000 },
  { position: { x: 0, y: 0, z: 0 }, rotation: { x: 0, y: 180, z: 0 }, duration: 2000 }
];

// 简单感知模拟路线数据
const forwardBackwardRouteData = [
  {
    timestamp: Date.now() + 32000,
    position: { x: 45.927308061452, y: 0, z: 119 },
    rotation: { x: 0, y: 0, z: 0 },   // 面向东方停车
    duration: 3000
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 65.927308061452, y: 0, z: 119 },
    rotation: { x: 0, y: 0, z: 0 },   // 面向东方停车
    duration: 30000
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 65.927308061452, y: 0, z: 119 },
    rotation: { x: 0, y: 0, z: 0 },   // 面向东方停车
    duration: 5000
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 125.927308061452, y: 0, z: 119 },
    rotation: { x: 0, y: 0, z: 0 },   // 面向东方停车
    duration: 20000
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 125.927308061452, y: 0, z: 119 },
    rotation: { x: 0, y: 0, z: 0 },   // 面向东方停车
    duration: 10000
  },
];

// 方法
const setDataSource = (source) => {
  dataSource.value = source;
  if (simulator.isRunning) {
    stopRoute();
  }
};

const loadSampleData = () => {
  jsonInput.value = JSON.stringify(sampleRouteData, null, 2);
};

// 加载排布模拟数据
const loadCityRouteData = () => {
  jsonInput.value = JSON.stringify(cityRouteData, null, 2);
};

// 加载能耗模拟路线数据
const loadCircularRouteData = () => {
  jsonInput.value = JSON.stringify(circularRouteData, null, 2);
};

// 加载感知模拟路线数据
const loadForwardBackwardRouteData = () => {
  console.log('Loading forward-backward route data');
  jsonInput.value = JSON.stringify(forwardBackwardRouteData, null, 2);
};

const loadJsonData = () => {
  try {
    const data = JSON.parse(jsonInput.value);
    if (simulator.loadRouteData(data)) {
      updateRouteStatus();
      console.log('Route data loaded successfully');
    }
  } catch (error) {
    alert('JSON格式错误: ' + error.message);
  }
};

const toggleWebSocket = () => {
  if (websocketConnected.value) {
    simulator.disconnect();
    websocketConnected.value = false;
  } else {
    if (simulator.connectWebSocket(websocketUrl.value)) {
      websocketConnected.value = true;
    }
  }
};

const startRoute = () => {
  console.log('Starting route...');
  console.log('Current model:', simulator.model);
  console.log('ModelComponent:', simulator.modelComponent);
  console.log('Route data length:', simulator.routeData.length);
  console.log('Can start:', canStart.value);
  console.log('Data source:', dataSource.value);

  // 详细检查启动条件
  if (!simulator.model) {
    console.error('No model available');
    alert('无法开始路线：未找到模型对象。请确保已选择模型并且模型已加载完成。');
    return;
  }

  if (dataSource.value === 'json' && simulator.routeData.length === 0) {
    console.error('No route data available');
    alert('无法开始路线：未加载路线数据。请点击"加载路线数据"按钮。');
    return;
  }

  if (dataSource.value === 'websocket' && !websocketConnected.value) {
    console.error('WebSocket not connected');
    alert('无法开始路线：WebSocket未连接。请检查连接状态。');
    return;
  }

  if (simulator.start()) {
    updateRouteStatus();
    console.log('Route started successfully');
  } else {
    console.error('Failed to start route - simulator.start() returned false');
    alert('路线启动失败：模拟器内部错误。请检查控制台日志。');
  }
};

const pauseRoute = () => {
  simulator.pause();
  updateRouteStatus();
};

const resumeRoute = () => {
  simulator.resume();
  updateRouteStatus();
};

const stopRoute = () => {
  simulator.stop();
  updateRouteStatus();
};

const updatePlaybackSpeed = () => {
  simulator.options.speed = playbackSpeed.value;
};

const updateInterpolationMode = () => {
  simulator.options.interpolationMode = interpolationMode.value;
};

const updateLoopMode = () => {
  simulator.options.loop = loopPlayback.value;
};

const updateSmoothRotation = () => {
  simulator.options.smoothRotation = smoothRotation.value;
};

const updateRouteStatus = () => {
  const status = simulator.getStatus();
  Object.assign(routeStatus, status);
  emit('status-change', status);
};

const getStatusClass = () => {
  if (routeStatus.isRunning && !routeStatus.isPaused) return 'running';
  if (routeStatus.isPaused) return 'paused';
  return 'stopped';
};

const getStatusText = () => {
  if (routeStatus.isRunning && !routeStatus.isPaused) return '运行中';
  if (routeStatus.isPaused) return '已暂停';
  return '已停止';
};

// 切换面板展开/关闭状态
const togglePanel = () => {
  isExpanded.value = !isExpanded.value;
};

// 生命周期
onMounted(() => {
  // 设置模型
  console.log('RouteController mounted, received model:', props.model);
  console.log('RouteController mounted, received modelComponent:', props.modelComponent);
  console.log('RouteController mounted, received sceneManagerRef:', props.sceneManagerRef);

  if (props.model) {
    simulator.setModel(props.model);
    console.log('Model set in simulator');
  }

  if (props.modelComponent) {
    simulator.setModelComponent(props.modelComponent);
    console.log('ModelComponent set in simulator');
  }

  if (props.sceneManagerRef) {
    simulator.setSceneManager(props.sceneManagerRef);
    console.log('SceneManager set in simulator');
  }

  // 设置回调
  simulator.onPositionUpdate = (data) => {
    if (data.position) {
      currentPosition.value = data.position;
    }
    if (data.rotation) {
      currentRotation.value = data.rotation;
    }
    emit('position-update', data);
  };

  simulator.onRouteComplete = () => {
    updateRouteStatus();
    console.log('Route completed');
  };

  simulator.onError = (error) => {
    console.error('Route simulator error:', error);
  };

  // 加载示例数据
  loadSampleData();
});

onBeforeUnmount(() => {
  simulator.destroy();
});

// 监听模型变化
watch(() => props.model, (newModel, oldModel) => {
  console.log('Model prop changed:', { old: oldModel, new: newModel });
  if (newModel) {
    simulator.setModel(newModel);
    console.log('Updated model in simulator');
  } else {
    console.log('Model prop is null/undefined');
  }
}, { immediate: true });

// 监听ModelComponent变化
watch(() => props.modelComponent, (newModelComponent, oldModelComponent) => {
  console.log('ModelComponent prop changed:', { old: oldModelComponent, new: newModelComponent });
  if (newModelComponent) {
    simulator.setModelComponent(newModelComponent);
    console.log('Updated ModelComponent in simulator');
  } else {
    console.log('ModelComponent prop is null/undefined');
  }
}, { immediate: true });
</script>

<style scoped>
.route-controller {
  background: #f5f5f5;
  border-radius: 8px;
  padding: 20px;
  margin: 10px 0;
}

/* 面板头部样式 */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #e8e8e8;
  border-bottom: 1px solid #ddd;
  cursor: pointer;
  transition: background-color 0.3s;
}

.panel-header:hover {
  background-color: #ddd;
}

.panel-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.toggle-icon {
  transition: transform 0.3s ease;
  color: #666;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

/* 面板内容样式 */
.panel-content {
  padding: 15px;
}

.panel-section h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.control-group {
  margin-bottom: 15px;
}

.control-group label {
  display: block;
  font-weight: bold;
  margin-bottom: 5px;
  color: #2c3e50;
}

.data-source-tabs {
  display: flex;
  gap: 5px;
}

.tab-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s;
}

.tab-btn.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.json-textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  resize: vertical;
}

.json-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.url-input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-right: 10px;
}

.connection-status {
  margin-top: 10px;
}

.status-indicator {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.status-indicator.connected {
  background: #d4edda;
  color: #155724;
}

.status-indicator.disconnected {
  background: #f8d7da;
  color: #721c24;
}

.control-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.btn.primary {
  background: #3498db;
  color: white;
}

.btn.secondary {
  background: #95a5a6;
  color: white;
}

.btn.danger {
  background: #e74c3c;
  color: white;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.progress-section {
  margin-top: 15px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 14px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #3498db;
  transition: width 0.3s;
}

.speed-slider {
  flex: 1;
  margin-right: 10px;
}

.speed-value {
  min-width: 40px;
  text-align: right;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.status-section {
  background: white;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.status-item {
  margin-bottom: 8px;
  font-size: 14px;
}

.status-text.running {
  color: #27ae60;
}

.status-text.paused {
  color: #f39c12;
}

.status-text.stopped {
  color: #e74c3c;
}
</style>
