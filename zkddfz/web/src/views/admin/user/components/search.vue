<template>
  <form class="flex gap-6" @submit.prevent="handleSubmit">
    <div class="flex gap-2">
      <a-button html-type="submit">查询</a-button>

      <a-button @click="reset">重置</a-button>
    </div>

    <div class="flex flex-wrap gap-x-6 gap-y-1">
      <div class="flex items-center gap-2 whitespace-nowrap">
        <label for="username">用户名</label>
        <a-select
          :filter-option="filterOption"
          :options="fullData?.map(v => ({ value: v.username }))"
          allow-clear
          class="min-w-48"
          id="username"
          show-search
          v-model:value="username"
        />
      </div>

      <div class="flex items-center gap-2 whitespace-nowrap">
        <label for="displayName">姓名</label>
        <a-select
          :filter-option="filterOption"
          :options="fullData?.map(v => ({ value: v.displayName }))"
          allow-clear
          class="min-w-48"
          id="displayName"
          show-search
          v-model:value="displayName"
        />
      </div>

      <div class="flex items-center gap-2 whitespace-nowrap">
        <label for="createdAtAfter">创建开始时间</label>
        <a-date-picker id="createdAtAfter" v-model:value="createdAtAfter" show-time allow-clear />
      </div>

      <div class="flex items-center gap-2 whitespace-nowrap">
        <label for="createdAtBefore">创建结束时间</label>
        <a-date-picker id="createdAtBefore" v-model:value="createdAtBefore" show-time allow-clear />
      </div>
    </div>
  </form>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue';
import type { Dayjs } from 'dayjs';
import { ref, type PropType } from 'vue';
import type { User } from '../libraries/types';

defineProps({
  fullData: {
    type: Array as PropType<User[]>,
  },
});

const emits = defineEmits(['search']);

const username = ref<string>();

const displayName = ref<string>();

const createdAtAfter = ref<Dayjs>();

const createdAtBefore = ref<Dayjs>();

const filterOption = (input: string, option: any) => {
  return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const init = () => {
  username.value = undefined;
  displayName.value = undefined;
  createdAtAfter.value = undefined;
  createdAtBefore.value = undefined;
};

const handleSubmit = () => {
  emits('search', {
    username: username.value || undefined,
    displayName: displayName.value || undefined,
    createdAtAfter: createdAtAfter.value || undefined,
    createdAtBefore: createdAtBefore.value || undefined,
  });
  message.success('查询条件已更新');
};

const reset = () => {
  init();
  handleSubmit();
};
</script>
