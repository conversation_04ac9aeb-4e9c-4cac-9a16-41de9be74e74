#!/bin/bash

echo "🚀 测试后端启动和WebSocket功能"
echo "=================================="

# 等待后端启动
echo "⏳ 等待后端服务启动..."
sleep 5

# 测试基本连接
echo "🔍 测试基本API连接..."
curl -s http://localhost:8080/api/websocket/status | jq '.' || echo "状态接口测试失败"

echo ""
echo "🚗 测试车辆数据接口..."
curl -s http://localhost:8080/api/websocket/vehicles | jq '.' || echo "车辆数据接口测试失败"

echo ""
echo "📡 测试手动广播..."
curl -s -X POST http://localhost:8080/api/websocket/broadcast | jq '.' || echo "广播接口测试失败"

echo ""
echo "🧪 测试发送测试消息..."
curl -s -X POST "http://localhost:8080/api/websocket/test?message=Hello%20from%20script" | jq '.' || echo "测试消息接口测试失败"

echo ""
echo "🔧 测试更新车辆数据..."
curl -s -X POST "http://localhost:8080/api/websocket/vehicles/device_22924?x=1000&z=-1000&rotationY=180&power=75&consume=25" | jq '.' || echo "车辆更新接口测试失败"

echo ""
echo "✅ 所有API测试完成！"
echo ""
echo "📋 可用的测试接口："
echo "  GET  /api/websocket/status           - 获取连接状态"
echo "  GET  /api/websocket/vehicles         - 获取车辆数据"
echo "  POST /api/websocket/broadcast        - 广播车辆控制电文"
echo "  POST /api/websocket/test             - 发送测试消息"
echo "  POST /api/websocket/vehicles/{id}    - 更新车辆数据"
echo ""
echo "🔌 WebSocket连接地址: ws://localhost:8080/route"
echo "🌐 测试页面: 打开 test-websocket.html"
