/**
 * 延时队列动画控制器
 * 实现基于队列的延时处理机制，简化实时数据同步
 */

export class SmoothAnimationController {
  constructor(options = {}) {
    // 配置选项
    this.options = {
      delayTime: 2000, // 延时时间（毫秒）
      processInterval: 1000, // 处理间隔（毫秒）
      uniformMotion: true, // 启用匀速运动模式
      ...options
    };

    // 数据队列系统
    this.dataQueues = new Map(); // device_id -> 数据队列
    this.animationStates = new Map(); // device_id -> 连续动画状态
    this.processTimers = new Map(); // device_id -> 处理定时器
    this.animationFrames = new Map(); // device_id -> 动画帧ID
    this.processingStates = new Map(); // device_id -> 处理状态
    
    // 性能监控
    this.performanceStats = {
      totalDataPoints: 0,
      processedDataPoints: 0,
      queueSizes: new Map()
    };

    console.log('延时队列动画控制器初始化完成', this.options);
  }

  /**
   * 添加新的数据点到延时队列
   */
  addDataPoint(deviceId, dataPoint) {
    const currentTime = Date.now();

    // 初始化设备的数据队列和处理状态
    if (!this.dataQueues.has(deviceId)) {
      this.dataQueues.set(deviceId, []);
      this.performanceStats.queueSizes.set(deviceId, 0);
      this.processingStates.set(deviceId, {
        isProcessing: false,
        firstDataTime: null,
        nextProcessTime: null
      });
    }

    // 添加时间戳
    const enhancedDataPoint = {
      ...dataPoint,
      receivedAt: currentTime
    };

    const queue = this.dataQueues.get(deviceId);
    const processingState = this.processingStates.get(deviceId);

    queue.push(enhancedDataPoint);

    this.performanceStats.totalDataPoints++;
    this.performanceStats.queueSizes.set(deviceId, queue.length);

    // 如果这是第一个数据点，设置延时开始处理
    if (!processingState.isProcessing && queue.length === 1) {
      processingState.firstDataTime = currentTime;
      processingState.nextProcessTime = currentTime + this.options.delayTime;
      console.log(`设备 ${deviceId} 第一个数据入队，将在 ${this.options.delayTime}ms 后开始处理`);

      // 设置延时处理第一个数据点
      setTimeout(() => {
        this.startFirstAnimation(deviceId);
      }, this.options.delayTime);
    } else {
      console.log(`设备 ${deviceId} 数据入队，队列长度: ${queue.length}`);
    }
  }

  /**
   * 启动第一个动画（延时后）
   */
  startFirstAnimation(deviceId) {
    console.log(`startFirstAnimation 被调用: ${deviceId}`);

    const queue = this.dataQueues.get(deviceId);
    const processingState = this.processingStates.get(deviceId);

    console.log(`队列状态: 存在=${!!queue}, 长度=${queue ? queue.length : 'N/A'}`);
    console.log(`处理状态: 存在=${!!processingState}`);

    if (!queue || !processingState || queue.length === 0) {
      console.log(`设备 ${deviceId} 延时后队列为空或状态无效`);
      return;
    }

    // 标记开始处理
    processingState.isProcessing = true;

    // 取出第一个数据点
    const dataPoint = queue.shift();
    this.performanceStats.processedDataPoints++;
    this.performanceStats.queueSizes.set(deviceId, queue.length);

    console.log(`设备 ${deviceId} 延时 ${this.options.delayTime}ms 后开始第一个动画: 位置=(${dataPoint.position.x}, ${dataPoint.position.z}), 剩余队列: ${queue.length}`);

    // 开始第一段动画
    this.animateToTarget(deviceId, dataPoint);
  }



  /**
   * 处理下一个动画段（基于duration的匀速插值）
   */
  animateToTarget(deviceId, targetData) {
    console.log(`animateToTarget 被调用: ${deviceId}, 目标位置=(${targetData.position.x}, ${targetData.position.z})`);

    // 停止当前动画
    this.stopAnimation(deviceId);

    // 获取当前位置
    let currentPosition;
    const existingState = this.animationStates.get(deviceId);
    if (existingState) {
      currentPosition = { ...existingState.currentPosition };
      console.log(`使用现有位置: (${currentPosition.x}, ${currentPosition.z})`);
    } else {
      // 首次动画，使用目标位置作为起始位置
      currentPosition = this.validatePosition(targetData.position);
      console.log(`首次动画，使用目标位置作为起始: (${currentPosition.x}, ${currentPosition.z})`);
    }

    const targetPosition = this.validatePosition(targetData.position);
    const targetRotation = targetData.rotation || { x: 0, y: 0, z: 0 };
    const duration = targetData.duration || 1000; // 默认1秒

    // 计算距离
    const dx = targetPosition.x - currentPosition.x;
    const dz = targetPosition.z - currentPosition.z;
    const distance = Math.sqrt(dx * dx + dz * dz);

    console.log(`设备 ${deviceId} 开始动画段: 从(${currentPosition.x.toFixed(1)}, ${currentPosition.z.toFixed(1)}) 到(${targetPosition.x.toFixed(1)}, ${targetPosition.z.toFixed(1)}), 距离=${distance.toFixed(1)}, 时长=${duration}ms`);

    // 创建动画状态
    const animationState = {
      deviceId,
      isRunning: true,
      startTime: performance.now(),
      duration: duration,
      startPosition: { ...currentPosition },
      targetPosition: { ...targetPosition },
      currentPosition: { ...currentPosition },
      startRotation: existingState ? { ...existingState.currentRotation } : { x: 0, y: 0, z: 0 },
      targetRotation: { ...targetRotation },
      currentRotation: existingState ? { ...existingState.currentRotation } : { x: 0, y: 0, z: 0 }
    };

    this.animationStates.set(deviceId, animationState);

    console.log(`动画状态已创建，准备启动duration动画`);

    // 启动基于duration的匀速动画
    this.startDurationBasedAnimation(deviceId, animationState);
  }

  /**
   * 启动基于duration的匀速动画
   */
  startDurationBasedAnimation(deviceId, animationState) {
    console.log(`启动设备 ${deviceId} 的duration动画，时长=${animationState.duration}ms`);

    const animate = () => {
      if (!animationState.isRunning) return;

      const currentTime = performance.now();
      const elapsed = currentTime - animationState.startTime;
      const progress = Math.min(elapsed / animationState.duration, 1);

      // 线性插值计算当前位置
      animationState.currentPosition = {
        x: this.lerp(animationState.startPosition.x, animationState.targetPosition.x, progress),
        y: animationState.startPosition.y, // Y坐标保持不变
        z: this.lerp(animationState.startPosition.z, animationState.targetPosition.z, progress)
      };

      // 线性插值计算当前旋转
      animationState.currentRotation = {
        x: this.lerpAngle(animationState.startRotation.x, animationState.targetRotation.x, progress),
        y: this.lerpAngle(animationState.startRotation.y, animationState.targetRotation.y, progress),
        z: this.lerpAngle(animationState.startRotation.z, animationState.targetRotation.z, progress)
      };

      // 触发位置更新回调
      if (this.onPositionUpdate) {
        const updateData = {
          deviceId,
          position: { ...animationState.currentPosition },
          rotation: { ...animationState.currentRotation },
          smooth: true,
          directUpdate: true,
          progress: progress,
          timestamp: currentTime
        };

        this.onPositionUpdate(updateData);
      }

      // 检查动画是否完成
      if (progress >= 1) {
        animationState.isRunning = false;
        console.log(`设备 ${deviceId} 动画段完成，到达位置 (${animationState.currentPosition.x.toFixed(1)}, ${animationState.currentPosition.z.toFixed(1)})`);

        // 动画完成后，检查队列中是否有下一个数据点
        this.processNextDataPoint(deviceId);
      } else {
        // 继续动画
        const frameId = requestAnimationFrame(animate);
        this.animationFrames.set(deviceId, frameId);
      }
    };

    // 开始动画
    animate();
  }

  /**
   * 处理下一个数据点（动画完成后立即处理）
   */
  processNextDataPoint(deviceId) {
    const queue = this.dataQueues.get(deviceId);
    const processingState = this.processingStates.get(deviceId);

    if (!queue || !processingState || queue.length === 0) {
      console.log(`设备 ${deviceId} 队列为空，等待新数据`);
      return;
    }

    // 动画完成后立即处理下一个数据点，不需要检查延时
    const dataPoint = queue.shift();
    this.performanceStats.processedDataPoints++;
    this.performanceStats.queueSizes.set(deviceId, queue.length);

    console.log(`设备 ${deviceId} 处理下一个数据点: 位置=(${dataPoint.position.x}, ${dataPoint.position.z}), 剩余队列: ${queue.length}`);

    // 立即开始下一段动画
    this.animateToTarget(deviceId, dataPoint);
  }



  /**
   * 更新设备状态（无动画）
   */
  updateDeviceState(deviceId, data) {
    if (this.onPositionUpdate) {
      this.onPositionUpdate({
        deviceId,
        position: data.position,
        rotation: data.rotation,
        smooth: false,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 检查位置是否相同
   */
  isSamePosition(pos1, pos2) {
    if (!pos1 || !pos2) return false;
    
    const threshold = 0.01; // 1cm的阈值
    return Math.abs(pos1.x - pos2.x) < threshold &&
           Math.abs(pos1.y - pos2.y) < threshold &&
           Math.abs(pos1.z - pos2.z) < threshold;
  }

  /**
   * 验证位置数据的有效性
   */
  validatePosition(position) {
    if (!position || typeof position !== 'object') {
      console.warn('⚠️ 无效的位置数据，使用默认值');
      return { x: 0, y: 0, z: 0 };
    }

    const validated = {
      x: this.validateNumber(position.x, 0),
      y: this.validateNumber(position.y, 0), // 直接使用原始Y值
      z: this.validateNumber(position.z, 0)
    };

    return validated;
  }

  /**
   * 验证数字有效性
   */
  validateNumber(value, defaultValue = 0) {
    if (typeof value !== 'number' || isNaN(value) || !isFinite(value)) {
      return defaultValue;
    }
    return value;
  }

  /**
   * 线性插值
   */
  lerp(start, end, t) {
    return start + (end - start) * t;
  }

  /**
   * 角度插值（处理角度环绕）
   */
  lerpAngle(start, end, t) {
    let diff = end - start;
    
    // 处理角度环绕（选择最短路径）
    if (diff > 180) {
      diff -= 360;
    } else if (diff < -180) {
      diff += 360;
    }
    
    return start + diff * t;
  }

  /**
   * 停止设备动画
   */
  stopAnimation(deviceId) {
    // 停止动画
    const animationState = this.animationStates.get(deviceId);
    if (animationState) {
      animationState.isRunning = false;
    }

    // 停止动画帧
    const frameId = this.animationFrames.get(deviceId);
    if (frameId) {
      cancelAnimationFrame(frameId);
      this.animationFrames.delete(deviceId);
    }

    console.log(`停止设备 ${deviceId} 的动画`);
  }

  /**
   * 停止所有动画
   */
  stopAllAnimations() {
    console.log('开始停止所有动画...');

    // 停止所有动画帧
    this.animationFrames.forEach((frameId, deviceId) => {
      cancelAnimationFrame(frameId);
      console.log(`停止设备 ${deviceId} 的动画帧`);
    });
    this.animationFrames.clear();



    // 清理所有数据
    this.dataQueues.clear();
    this.animationStates.clear();
    this.processingStates.clear();
    this.performanceStats.queueSizes.clear();

    console.log('所有动画已停止，数据已清理');
  }

  /**
   * 清理设备数据
   */
  clearDevice(deviceId) {
    this.stopAnimation(deviceId);
    this.dataQueues.delete(deviceId);
    this.animationStates.delete(deviceId);
    this.processingStates.delete(deviceId);
    this.performanceStats.queueSizes.delete(deviceId);
    console.log(`清理设备 ${deviceId} 的数据`);
  }

  /**
   * 清理所有数据
   */
  clearAll() {
    this.stopAllAnimations();
    console.log('清理所有数据');
  }

  /**
   * 设置位置更新回调
   */
  setPositionUpdateCallback(callback) {
    this.onPositionUpdate = callback;
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats() {
    return { ...this.performanceStats };
  }

  /**
   * 获取设备状态
   */
  getDeviceStatus(deviceId) {
    const queue = this.dataQueues.get(deviceId);
    const timer = this.processTimers.has(deviceId);
    const frame = this.animationFrames.has(deviceId);
    const processingState = this.processingStates.get(deviceId);
    const animationState = this.animationStates.get(deviceId);

    return {
      queueSize: queue ? queue.length : 0,
      hasTimer: timer,
      hasAnimation: frame,
      isProcessing: processingState ? processingState.isProcessing : false,
      nextProcessTime: processingState ? processingState.nextProcessTime : null,
      isAnimating: animationState ? animationState.isRunning : false,
      currentPosition: animationState ? animationState.currentPosition : null,
      targetPosition: animationState ? animationState.targetPosition : null,
      delayTime: this.options.delayTime,
      processInterval: this.options.processInterval
    };
  }
}

export default SmoothAnimationController;
