package com.baosight.api.constants;

public final class ApiPaths {

  /**
   * 私有构造函数，防止实例化
   */
  private ApiPaths() {
  }

  /**
   * API v1 版本路径匹配规则
   */
  public static final String API_V1_PATTERN = "/api/v1/**";

  /**
   * 用户相关 API 路径
   */
  public static final class User {

    /**
     * 私有构造函数，防止实例化
     */
    private User() {
    }

    /**
     * 登录
     */
    public static final String LOGIN = "/api/v1/user/login";

    public static final String test = "/api/v1/test/test";

    /**
     * 登出
     */
    public static final String LOGOUT = "/api/v1/user/logout";

    /**
     * 会话登录 (自动登录)
     */
    public static final String SESSION = "/api/v1/user/session";
  }

  /**
   * 管理员相关 API 路径
   */
  public static final class Admin {

    /**
     * 私有构造函数，防止实例化
     */
    private Admin() {
    }

    /**
     * 管理员路径匹配规则
     */
    public static final String PATTERN = "/api/v1/admin/**";

    /**
     * 获取用户
     */
    public static final String GET_USERS = "/api/v1/admin/user/get_users";

    /**
     * 新增用户
     */
    public static final String ADD_USER = "/api/v1/admin/user/add_user";

    /**
     * 修改用户
     */
    public static final String UPDATE_USER = "/api/v1/admin/user/update_user";

    /**
     * 删除用户
     */
    public static final String DELETE_USER = "/api/v1/admin/user/delete_user";

    /**
     * 获取角色
     */
    public static final String GET_ROLES = "/api/v1/admin/user/get_roles";

    /**
     * 获取用户-角色
     */
    public static final String GET_USER_ROLES = "/api/v1/admin/user/get_user_roles";

    /**
     * 修改用户-角色
     */
    public static final String UPDATE_USER_ROLES = "/api/v1/admin/user/update_user_roles";

    /**
     * 获取操作记录
     */
    public static final String GET_ACTIVITIES = "/api/v1/admin/activity/get_activities";
  }
}
