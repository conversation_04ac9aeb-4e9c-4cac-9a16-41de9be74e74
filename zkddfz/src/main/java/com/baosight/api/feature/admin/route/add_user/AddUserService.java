package com.baosight.api.feature.admin.route.add_user;

import java.util.ArrayList;
import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import com.baosight.api.db.role.RoleNameEnums;
import com.baosight.api.db.user.UserEntity;
import com.baosight.api.db.user.UserRepository;
import com.baosight.api.db.user_role.UserRoleEntity;
import com.baosight.api.db.user_role.UserRoleRepository;
import com.baosight.api.feature.user.security.PasswordService;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class AddUserService {
  private final PasswordService passwordService;

  private final UserRepository userRepository;

  private final UserRoleRepository userRoleRepository;

  private void checkUserCanBeAdded(AddUserRequestBody requestBody) {
    userRepository.findByUsername(requestBody.getUsername()).ifPresent(user -> {
      throw new ResponseStatusException(HttpStatus.UNPROCESSABLE_ENTITY, "用户名已存在");
    });
  }

  private void checkRolesExist(AddUserRequestBody requestBody) {
    for (String roleName : requestBody.getRoles()) {
      try {
        RoleNameEnums.valueOf(roleName);
      } catch (IllegalArgumentException e) {
        throw new ResponseStatusException(HttpStatus.UNPROCESSABLE_ENTITY, roleName + "角色不存在");
      }
    }
  }

  private void insertUser(AddUserRequestBody requestBody) {
    UserEntity userEntity = new UserEntity();
    userEntity.setUsername(requestBody.getUsername());
    userEntity.setPasswordHash(passwordService.hash(requestBody.getPassword()));
    userEntity.setDisplayName(requestBody.getDisplayName());

    userRepository.save(userEntity);
  }

  private void insertUserRoles(AddUserRequestBody requestBody) {
    List<UserRoleEntity> userRoleEntities = new ArrayList<>();
    for (String role : requestBody.getRoles()) {
      UserRoleEntity userRoleEntity = new UserRoleEntity();
      userRoleEntity.setUsername(requestBody.getUsername());
      userRoleEntity.setRoleName(RoleNameEnums.valueOf(role));
      userRoleEntities.add(userRoleEntity);
    }

    userRoleRepository.saveAll(userRoleEntities);
  }

  @Transactional
  public void addUser(AddUserRequestBody requestBody) {
    checkUserCanBeAdded(requestBody);
    checkRolesExist(requestBody);

    insertUser(requestBody);
    insertUserRoles(requestBody);
  }
}
