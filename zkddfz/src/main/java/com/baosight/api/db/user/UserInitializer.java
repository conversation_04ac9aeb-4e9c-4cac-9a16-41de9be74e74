package com.baosight.api.db.user;

import org.springframework.stereotype.Component;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class UserInitializer {
  private static final String PASSWORD_HASH_123456 = "$2a$10$epK.owIgd/Sz2LCWEIsHpeNKwrgIO4uYj.cO4cY09nMrKqJVZfGUO";

  private final UserRepository userRepository;

  private void upsert(String username, String displayName) {
    UserEntity userEntity = userRepository.findByUsername(username)
        .orElse(new UserEntity());

    userEntity.setUsername(username);
    userEntity.setDisplayName(displayName);
    userEntity.setPasswordHash(PASSWORD_HASH_123456);

    userRepository.save(userEntity);
  }

  @Transactional
  public void init() {
    upsert("root", "root");
    upsert("user", "user");
  }
}
