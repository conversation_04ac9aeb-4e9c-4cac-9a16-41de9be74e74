<template>
  <div class="tech-card-new text-white p-6 rounded-lg shadow-lg max-w-sm mx-auto relative overflow-hidden">
    <!-- 顶部标题区域，模拟图片中的标题栏 -->
    <div
      class="relative bg-gradient-to-b from-blue-900 to-blue-950 border-b border-blue-700 rounded-t-lg -mx-6 -mt-6 px-6 py-3 mb-6"
    >
      <h1 class="text-xl font-bold text-blue-200">{{ props.model.name }}</h1>
      <!-- 模拟标题栏右侧的突出部分 -->
      <div class="absolute right-0 top-0 h-full w-8 bg-blue-800 transform skew-x-12 origin-top-left -mr-4"></div>
    </div>

    <div class="space-y-6 mb-6">
      <!-- 初始坐标和角度部分 -->
      <div>
        <h3 class="text-base font-semibold text-blue-300 mb-3">初始坐标和角度</h3>
        <div class="space-y-3">
          <!-- 位置 -->
          <div>
            <label class="block text-sm font-medium text-gray-400 mb-1">位置:</label>
            <div class="flex justify-between gap-2">
              <!-- 调整为 justify-between 和更小的 gap -->
              <div class="flex items-center bg-blue-950 border border-blue-800 rounded-md px-2 py-1 w-28">
                <!-- 固定宽度 w-28 -->
                <span class="w-5 text-blue-300 text-xs">X:</span>
                <input
                  type="number"
                  v-model.number="props.model.initialPosition.x"
                  class="flex-1 bg-transparent text-blue-100 text-sm focus:outline-none"
                />
              </div>
              <div class="flex items-center bg-blue-950 border border-blue-800 rounded-md px-2 py-1 w-28">
                <!-- 固定宽度 w-28 -->
                <span class="w-5 text-blue-300 text-xs">Y:</span>
                <input
                  type="number"
                  v-model.number="props.model.initialPosition.y"
                  class="flex-1 bg-transparent text-blue-100 text-sm focus:outline-none"
                />
              </div>
              <div class="flex items-center bg-blue-950 border border-blue-800 rounded-md px-2 py-1 w-28">
                <!-- 固定宽度 w-28 -->
                <span class="w-5 text-blue-300 text-xs">Z:</span>
                <input
                  type="number"
                  v-model.number="props.model.initialPosition.z"
                  class="flex-1 bg-transparent text-blue-100 text-sm focus:outline-none"
                />
              </div>
            </div>
          </div>
          <!-- 角度 -->
          <div>
            <label class="block text-sm font-medium text-gray-400 mb-1">角度:</label>
            <div class="flex justify-between gap-2">
              <!-- 调整为 justify-between 和更小的 gap -->
              <div class="flex items-center bg-blue-950 border border-blue-800 rounded-md px-2 py-1 w-28">
                <!-- 固定宽度 w-28 -->
                <span class="w-5 text-blue-300 text-xs">X:</span>
                <input
                  type="number"
                  v-model.number="props.model.initialRotation.x"
                  class="flex-1 bg-transparent text-blue-100 text-sm focus:outline-none"
                />
              </div>
              <div class="flex items-center bg-blue-950 border border-blue-800 rounded-md px-2 py-1 w-28">
                <!-- 固定宽度 w-28 -->
                <span class="w-5 text-blue-300 text-xs">Y:</span>
                <input
                  type="number"
                  v-model.number="props.model.initialRotation.y"
                  class="flex-1 bg-transparent text-blue-100 text-sm focus:outline-none"
                />
              </div>
              <div class="flex items-center bg-blue-950 border border-blue-800 rounded-md px-2 py-1 w-28">
                <!-- 固定宽度 w-28 -->
                <span class="w-5 text-blue-300 text-xs">Z:</span>
                <input
                  type="number"
                  v-model.number="props.model.initialRotation.z"
                  class="flex-1 bg-transparent text-blue-100 text-sm focus:outline-none"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 当前坐标和角度 (只读) 部分 -->
      <div>
        <h3 class="text-base font-semibold text-blue-300 mb-3">当前坐标和角度 (只读)</h3>
        <div class="space-y-2">
          <div class="bg-blue-950 border border-blue-800 text-blue-100 px-4 py-2 rounded-md text-sm font-mono">
            位置: <span class="text-orange-400">{{ props.model.currentPosition.x.toFixed(2) }}</span
            >, <span class="text-orange-400">{{ props.model.currentPosition.y.toFixed(2) }}</span
            >, <span class="text-orange-400">{{ props.model.currentPosition.z.toFixed(2) }}</span>
          </div>
          <div class="bg-blue-950 border border-blue-800 text-blue-100 px-4 py-2 rounded-md text-sm font-mono">
            角度: <span class="text-green-400">{{ props.model.currentRotation.x.toFixed(1) }}°</span>,
            <span class="text-green-400">{{ props.model.currentRotation.y.toFixed(1) }}°</span>,
            <span class="text-green-400">{{ props.model.currentRotation.z.toFixed(1) }}°</span>
          </div>
        </div>
      </div>
    </div>

    <div class="flex gap-3 mt-6">
      <button
        @click="resetModelToInitial(props.model.id)"
        class="flex-1 py-2 px-3 rounded-md text-white text-sm font-semibold transition-all duration-200 bg-gradient-to-b from-blue-600 to-blue-800 border border-blue-500 shadow-md hover:from-blue-500 hover:to-blue-700 hover:border-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-blue-950"
      >
        重置到初始位置
      </button>
      <button
        @click="setCurrentAsInitial(props.model.id)"
        class="flex-1 py-2 px-3 rounded-md text-white text-sm font-semibold transition-all duration-200 bg-gradient-to-b from-red-600 to-red-800 border border-red-500 shadow-md hover:from-red-500 hover:to-red-700 hover:border-red-400 focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-offset-2 focus:ring-offset-blue-950"
      >
        设当前为初始位置
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Model {
  id: string;
  name: string;
  initialPosition: { x: number; y: number; z: number }; // 地面坐标系：Y=0表示放在地面上
  initialRotation: { x: number; y: number; z: number };
  currentPosition: { x: number; y: number; z: number };
  currentRotation: { x: number; y: number; z: number };
}

interface Props {
  model: Model;
  loadingStatus: Record<string, 'loaded' | 'loading' | 'error'>;
  firstPersonView: { targetModelId: string | null };
}

const props = defineProps<Props>();

// 重置模型到初始位置和角度
const resetModelToInitial = (modelId: string) => {
  // if (sceneManagerRef.value) {
  //   const modelRef = sceneManagerRef.value.modelRefs?.[modelId];
  //   if (modelRef && modelRef.resetToInitialTransform) {
  //     modelRef.resetToInitialTransform();
  //     if (props.model) {
  //       props.model.currentPosition = { ...props.model.initialPosition };
  //       props.model.currentRotation = { ...props.model.initialRotation };
  //     }
  //     console.log(`Model ${modelId} reset to initial transform`);
  //   }
  // }
};

// 将当前位置设为初始位置
const setCurrentAsInitial = (modelId: string) => {
  if (props.model && sceneManagerRef.value) {
    const modelRef = sceneManagerRef.value.modelRefs?.[modelId];
    if (modelRef) {
      // 获取当前位置和角度
      const currentPos = modelRef.getCurrentPosition();
      const currentRot = modelRef.getCurrentRotation();
      if (currentPos && currentRot) {
        // 更新初始坐标
        props.model.initialPosition = { ...currentPos };
        props.model.initialRotation = { ...currentRot };
        console.log(`Set current position as initial for model ${modelId}:`, currentPos, currentRot);
      }
    }
  }
};
</script>

<style scoped>
/* 隐藏数字输入框的默认箭头 */
input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type='number'] {
  -moz-appearance: textfield; /* Firefox */
}

/* 新的科技感卡片样式 */
.tech-card-new {
  background-color: rgba(28, 38, 58, 0.9); /* 深蓝色背景，高透明度 */
  border: 1px solid rgba(59, 130, 246, 0.4); /* 蓝色边框，更透明 */
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.5); /* 蓝色发光效果 */
}

/* 模拟图片中卡片内部的微妙纹理或光泽 */
.tech-card-new::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 0%, rgba(59, 130, 246, 0.05) 0%, transparent 70%);
  pointer-events: none;
  z-index: -1;
}
</style>
