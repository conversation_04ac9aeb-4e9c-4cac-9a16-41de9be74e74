<template>
  <ag-grid-vue
    :animate-rows="animateRows"
    :default-col-def="defaultColDef"
    :enable-cell-text-selection="enableCellTextSelection"
    :header-height="headerHeight"
    :locale-text="AG_GRID_LOCALE_CN"
    :row-data="rowData"
    :row-height="rowHeight"
    :suppress-row-hover-highlight="suppressRowHoverHighlight"
    :suppress-scroll-on-new-data="suppressScrollOnNewData"
    :theme="theme"
    :tooltip-hide-delay="tooltipHideDelay"
    :tooltip-show-delay="tooltipShowDelay"
  />
</template>

<script setup lang="ts">
import { AG_GRID_LOCALE_CN } from '@ag-grid-community/locale';
import { useDark } from '@vueuse/core';
import { colorSchemeDark, colorSchemeLight, ModuleRegistry, themeAlpine, themeBalham } from 'ag-grid-community';
import { AllEnterpriseModule } from 'ag-grid-enterprise';
import { AgGridVue } from 'ag-grid-vue3';
import { computed, onMounted, toRefs, type PropType } from 'vue';

ModuleRegistry.registerModules([AllEnterpriseModule]);

const props = defineProps({
  animateRows: {
    type: Boolean,
    default: true,
  },
  defaultColDef: {
    type: Object as PropType<{ sortable?: boolean; filter?: boolean; resizable?: boolean }>,
    default: () => ({ sortable: true, filter: true, resizable: true }),
  },
  enableCellTextSelection: {
    type: Boolean,
    default: true,
  },
  suppressRowHoverHighlight: {
    type: Boolean,
    default: false,
  },
  suppressScrollOnNewData: {
    type: Boolean,
    default: true,
  },
  rowData: {
    type: Array as PropType<any[]>,
    default: [],
  },
  rowHeight: {
    type: Number,
  },
  headerHeight: {
    type: Number,
  },
  /**
   * 如果提供了 true/false，则不会根据系统设置变更深色模式
   */
  dark: {
    type: Boolean,
    default: undefined,
  },
  dense: {
    type: Boolean,
  },
  tooltipShowDelay: {
    type: Number,
    default: 2000,
  },
  tooltipHideDelay: {
    type: Number,
    default: 2000,
  },
});

const { dark, dense } = toRefs(props);

const isDark = useDark();

const getTheme = (dark: boolean, dense?: boolean) => {
  if (dense) {
    return dark ? themeBalham.withPart(colorSchemeDark) : themeBalham.withPart(colorSchemeLight);
  }
  return dark ? themeAlpine.withPart(colorSchemeDark) : themeAlpine.withPart(colorSchemeLight);
};

const theme = computed(() =>
  typeof dark?.value === 'boolean' ? getTheme(dark.value, dense.value) : getTheme(isDark.value, dense.value),
);

onMounted(() => {
  document.getElementsByClassName('ag-watermark')?.[0]?.remove();
});
</script>
