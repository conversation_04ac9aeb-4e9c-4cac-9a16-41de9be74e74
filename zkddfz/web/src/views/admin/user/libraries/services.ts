import { api } from '@/common/api';
import type { ConditionMap } from '@/common/types';
import { dateFilterParams, dateTimeValueFormatter } from '@/components/ag-grid';
import { ApiPaths } from '@/constants/api-paths';
import type { ValueFormatterParams } from 'ag-grid-community';
import type { Ref } from 'vue';
import type { Role, RoleDTO, User, UserDTO, UserRole, UserRoleDTO } from './types';

export const getUsers = async (conditionMap?: Ref<ConditionMap | undefined>): Promise<User[]> => {
  const payload = conditionMap?.value ?? {};
  const { data } = await api.post<UserDTO[]>(ApiPaths.Admin.GET_USERS, payload);

  return data.map((v, i) => ({ rowNumber: i + 1, ...v }));
};

export const getUserByUsername = async (username: string): Promise<User> => {
  const { data } = await api.post<UserDTO[]>(ApiPaths.Admin.GET_USERS, { username });
  return data.map((v, i) => ({ rowNumber: i + 1, ...v }))[0];
};

export const getRoles = async (): Promise<Role[]> => {
  const { data } = await api.post<RoleDTO[]>(ApiPaths.Admin.GET_ROLES);
  return data;
};

export const getUserRoles = async (username: string): Promise<UserRole[]> => {
  const { data } = await api.post<UserRoleDTO[]>(ApiPaths.Admin.GET_USER_ROLES, { username });
  return data;
};

export const deleteUserByUsername = async (username: string) => {
  await api.post(ApiPaths.Admin.DELETE_USER, { username });
};

export const createColumnDefs = () => [
  { headerName: '序号', field: 'rowNumber', filter: 'agNumberColumnFilter', maxWidth: 128 },

  { headerName: '用户名', field: 'username', maxWidth: 128 },

  { headerName: '姓名', field: 'displayName', maxWidth: 128 },

  {
    headerName: '创建时间',
    field: 'createdAt',
    filter: 'agDateColumnFilter',
    filterParams: dateFilterParams,
    valueFormatter: dateTimeValueFormatter,
    maxWidth: 176,
  },

  {
    headerName: '用户角色列表',
    field: 'roles',
    filter: 'agTextColumnFilter',
    valueFormatter: (params: ValueFormatterParams) => params.value.join(','),
  },
];
