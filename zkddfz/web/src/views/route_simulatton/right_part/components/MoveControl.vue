<script setup lang="ts">
import { computed, ref } from 'vue';
import ModelControlPanel from './ModelControlPanel.vue';

interface ModelData {
  id: string;
  name: string;
  currentPosition: { x: number; z: number };
  currentRotation: { y: number };
}

const props = defineProps({
  // 模型配置数组
  models: {
    type: Array,
    default: () => [],
  },
  // SceneManager引用
  sceneManagerRef: {
    type: Object,
    default: null,
  },
});

const emit = defineEmits(['model-selected', 'movement-started', 'movement-stopped']);

// 处理模型选择事件
const handleModelSelected = (modelId) => {
  emit('model-selected', modelId);
};

// 处理移动开始事件
const handleMovementStarted = (data) => {
  emit('movement-started', data);
};

// 处理移动停止事件
const handleMovementStopped = (data) => {
  emit('movement-stopped', data);
};



const loadingStatus = ref<Record<string, 'loaded' | 'loading' | 'error'>>({
  car1: 'loaded',
  drone2: 'loading',
  robot3: 'error',
});

const availableModelsForControl = computed(() =>
  props.models.map(model => {
    // 自动将新模型标记为已加载状态
    if (!loadingStatus.value[model.id]) {
      loadingStatus.value[model.id] = 'loaded';
    }
    return {
      id: model.id,
      name: model.name,
      status: loadingStatus.value[model.id] || 'loaded',
    };
  }),
);
</script>

<template>
  <ModelControlPanel
    :availableModels="availableModelsForControl"
    :sceneManagerRef="props.sceneManagerRef"
    @model-selected="handleModelSelected"
    @movement-started="handleMovementStarted"
    @movement-stopped="handleMovementStopped"
  />
</template>
