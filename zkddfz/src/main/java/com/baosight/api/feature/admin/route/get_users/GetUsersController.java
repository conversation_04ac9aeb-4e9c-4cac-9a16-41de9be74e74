package com.baosight.api.feature.admin.route.get_users;

import java.util.List;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baosight.api.constants.ApiPaths;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
public class GetUsersController {
  private final GetUsersService getUsersService;

  @PostMapping(ApiPaths.Admin.GET_USERS)
  public List<GetUsersResponseBody> handleRequest(@Valid @RequestBody GetUsersRequestBody requestBody) {
    return getUsersService.getUsers(requestBody);
  }
}
