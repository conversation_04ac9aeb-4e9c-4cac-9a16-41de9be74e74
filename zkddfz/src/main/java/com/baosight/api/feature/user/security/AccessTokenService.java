package com.baosight.api.feature.user.security;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Date;

import javax.crypto.SecretKey;

import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import jakarta.annotation.PostConstruct;
import jakarta.servlet.http.Cookie;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class AccessTokenService {
  private static final String ACCESS_TOKEN_COOKIE_NAME = "access_token";

  private SecretKey accessTokenSecretKey;

  @PostConstruct
  public void init() {
    byte[] keyBytes = ACCESS_TOKEN_COOKIE_NAME.getBytes(StandardCharsets.UTF_8); // 密钥默认与 cookie 名相同, 可按需修改
    if (keyBytes.length < 32) {
      keyBytes = Arrays.copyOf(keyBytes, 32);
    }
    this.accessTokenSecretKey = Keys.hmacShaKeyFor(keyBytes);
  }

  private String createAccessToken(String username) {
    return Jwts.builder()
        .subject(username)
        .signWith(accessTokenSecretKey)
        .issuedAt(new Date())
        .compact();
  }

  public String validateAccessToken(String token) {
    try {
      return Jwts.parser()
          .verifyWith(accessTokenSecretKey)
          .build()
          .parseSignedClaims(token)
          .getPayload()
          .getSubject();
    } catch (Exception e) {
      throw new ResponseStatusException(HttpStatus.UNAUTHORIZED);
    }
  }

  public Cookie createAccessTokenCookie(String username) {
    Cookie cookie = new Cookie(ACCESS_TOKEN_COOKIE_NAME, createAccessToken(username));
    cookie.setMaxAge(Integer.MAX_VALUE); // 时长设为最大整数值，即永不过期
    cookie.setHttpOnly(true); // 仅服务器可用
    cookie.setSecure(false); // 允许 HTTP 使用
    cookie.setPath("/"); // 面向整个应用

    return cookie;
  }

  public Cookie createLogoutCookie() {
    Cookie cookie = new Cookie(ACCESS_TOKEN_COOKIE_NAME, "");

    cookie.setMaxAge(0);
    cookie.setHttpOnly(true);
    cookie.setSecure(false);
    cookie.setPath("/");

    return cookie;
  }

  public String getAccessTokenCookieName() {
    return ACCESS_TOKEN_COOKIE_NAME;
  }
}
