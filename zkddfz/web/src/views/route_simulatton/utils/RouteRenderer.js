import * as THREE from 'three';

/**
 * 路线渲染管理器
 * 负责解析路线电文数据并在3D场景中渲染导航路线
 */
export class RouteRenderer {
  constructor(scene) {
    this.scene = scene;
    this.routes = new Map(); // device_id -> route object
    this.animationFrameId = null;
    this.clock = new THREE.Clock();
    
    console.log('路线渲染管理器初始化完成');
  }

  /**
   * 解析路线电文数据并创建路线
   * @param {Array} routeData - 路线电文数据
   */
  parseAndCreateRoutes(routeData) {
    console.log('开始解析路线电文数据:', routeData);

    // 不清除现有路线，而是更新或添加新路线
    routeData.forEach(deviceRoute => {
      // 如果该设备已有路线，先清除
      if (this.routes.has(deviceRoute.device_id)) {
        this.removeRoute(deviceRoute.device_id);
      }
      // 创建新路线
      this.createRoute(deviceRoute.device_id, deviceRoute.path);
    });

    // 启动动画
    this.startAnimation();
  }

  /**
   * 移除指定设备的路线
   * @param {string} deviceId - 设备ID
   */
  removeRoute(deviceId) {
    const route = this.routes.get(deviceId);
    if (route) {
      this.scene.remove(route.group);

      // 清理几何体和材质
      route.group.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          if (child.geometry) child.geometry.dispose();
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach(material => material.dispose());
            } else {
              child.material.dispose();
            }
          }
        }
      });

      this.routes.delete(deviceId);
      console.log(`设备 ${deviceId} 的路线已移除`);
    }
  }

  /**
   * 为指定设备创建路线
   * @param {string} deviceId - 设备ID
   * @param {Array} pathPoints - 路径点数组
   */
  createRoute(deviceId, pathPoints) {
    if (pathPoints.length < 2) {
      console.warn(`设备 ${deviceId} 的路径点少于2个，无法创建路线`);
      return;
    }

    console.log(`为设备 ${deviceId} 创建路线，路径点数量: ${pathPoints.length}`);

    // 创建路线组
    const routeGroup = new THREE.Group();
    routeGroup.name = `route_${deviceId}`;
    routeGroup.visible = false; // 默认隐藏

    // 创建高德导航样式路线
    this.createGaodeStyleRoute(routeGroup, pathPoints);

    // 添加路径点标记
    this.addPathMarkers(routeGroup, pathPoints);

    // 保存路线信息
    this.routes.set(deviceId, {
      group: routeGroup,
      visible: false
    });

    // 添加到场景
    this.scene.add(routeGroup);

    console.log(`设备 ${deviceId} 的路线创建完成`);
  }

  /**
   * 创建地面道路样式路线
   * @param {THREE.Group} routeGroup - 路线组
   * @param {Array} pathPoints - 路径点数组
   */
  createGaodeStyleRoute(routeGroup, pathPoints) {
    // 创建路径曲线
    const curve = this.createPathCurve(pathPoints);
    if (!curve) return;

    // 创建地面道路路线（平面几何体）
    const routeGeometry = this.createRoadGeometry(curve);
    const routeMaterial = this.createRoadMaterial();
    const roadMesh = new THREE.Mesh(routeGeometry, routeMaterial);
    roadMesh.position.y = 0.1; // 稍微高于地面，避免z-fighting
    roadMesh.rotation.x = -Math.PI / 2; // 平铺在地面上
    routeGroup.add(roadMesh);

    // 保存材质和曲线用于动画更新
    routeGroup.userData.routeMaterial = routeMaterial;
    routeGroup.userData.curve = curve;
  }

  /**
   * 创建道路几何体
   * @param {THREE.CatmullRomCurve3} curve - 路径曲线
   * @returns {THREE.PlaneGeometry} 道路几何体
   */
  createRoadGeometry(curve) {
    // 获取曲线上的点
    const points = curve.getPoints(200);
    const roadWidth = 20; // 道路宽度20米

    // 创建道路几何体
    const vertices = [];
    const uvs = [];
    const indices = [];

    for (let i = 0; i < points.length; i++) {
      const point = points[i];
      const nextPoint = points[i + 1];

      // 计算道路方向
      let direction;
      if (nextPoint) {
        direction = new THREE.Vector3().subVectors(nextPoint, point).normalize();
      } else {
        direction = new THREE.Vector3().subVectors(point, points[i - 1]).normalize();
      }

      // 计算垂直方向（道路宽度方向）
      const perpendicular = new THREE.Vector3(-direction.z, 0, direction.x).normalize();

      // 创建道路两侧的顶点
      const leftPoint = point.clone().add(perpendicular.clone().multiplyScalar(roadWidth / 2));
      const rightPoint = point.clone().add(perpendicular.clone().multiplyScalar(-roadWidth / 2));

      // 添加顶点
      vertices.push(leftPoint.x, leftPoint.z, 0); // 注意：这里z坐标为0，因为我们会旋转到地面
      vertices.push(rightPoint.x, rightPoint.z, 0);

      // 添加UV坐标
      const u = i / (points.length - 1);
      uvs.push(u, 0);
      uvs.push(u, 1);

      // 添加三角形索引
      if (i < points.length - 1) {
        const baseIndex = i * 2;
        // 第一个三角形
        indices.push(baseIndex, baseIndex + 1, baseIndex + 2);
        // 第二个三角形
        indices.push(baseIndex + 1, baseIndex + 3, baseIndex + 2);
      }
    }

    const geometry = new THREE.BufferGeometry();
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
    geometry.setIndex(indices);
    geometry.computeVertexNormals();

    return geometry;
  }

  /**
   * 创建路径曲线（90度直角）
   * @param {Array} pathPoints - 路径点数组
   * @returns {THREE.CatmullRomCurve3} 路径曲线
   */
  createPathCurve(pathPoints) {
    if (pathPoints.length < 2) {
      return null;
    }

    console.log('创建路径曲线，原始路径点:', pathPoints);

    // 创建直角路径，每个转弯点都是90度
    const straightSegments = [];

    for (let i = 0; i < pathPoints.length - 1; i++) {
      const current = pathPoints[i];
      const next = pathPoints[i + 1];

      // 创建从当前点到下一点的直线段
      const segmentPoints = [];

      // 起点 - 直接使用原始坐标，不做转换
      segmentPoints.push(new THREE.Vector3(current.x, 0, current.z));

      // 如果是直角转弯，添加中间点确保90度角
      if (i < pathPoints.length - 2) {
        const afterNext = pathPoints[i + 2];

        // 检查是否需要90度转弯
        const dir1 = new THREE.Vector2(next.x - current.x, next.z - current.z).normalize();
        const dir2 = new THREE.Vector2(afterNext.x - next.x, afterNext.z - next.z).normalize();

        // 如果方向不同，创建90度转弯
        if (Math.abs(dir1.dot(dir2)) < 0.99) {
          // 在转弯点前添加一个点，确保直角
          const beforeTurn = new THREE.Vector3(
            next.x - dir1.x * 2,
            0,
            next.z - dir1.y * 2
          );
          segmentPoints.push(beforeTurn);
        }
      }

      // 终点 - 直接使用原始坐标，不做转换
      segmentPoints.push(new THREE.Vector3(next.x, 0, next.z));

      straightSegments.push(...segmentPoints);
    }

    console.log('生成的路径段点:', straightSegments.map(p => ({x: p.x, y: p.y, z: p.z})));

    // 使用线性插值创建直角路径
    return new THREE.CatmullRomCurve3(straightSegments, false, 'centripetal', 0);
  }

  /**
   * 创建道路材质（高德蓝 + 白色描边 + 箭头指示动画）
   * @returns {THREE.ShaderMaterial} 道路材质
   */
  createRoadMaterial() {
    return new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        gaodeBlue: { value: new THREE.Color(0x1989FA) }, // 高德蓝 #1989FA
        white: { value: new THREE.Color(0xffffff) }, // 白色描边
        arrowColor: { value: new THREE.Color(0xffffff) } // 箭头颜色
      },
      vertexShader: `
        varying vec2 vUv;

        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform float time;
        uniform vec3 gaodeBlue;
        uniform vec3 white;
        uniform vec3 arrowColor;
        varying vec2 vUv;

        // 创建回旋镖形状的箭头
        float createBoomerangArrow(vec2 uv, float size) {
          // 回旋镖的中心主体
          float centerBody = step(-size * 0.8, uv.x) * step(uv.x, size * 0.2) *
                            step(-size * 0.15, uv.y) * step(uv.y, size * 0.15);

          // 上臂（向上弯曲）
          vec2 upperArm = uv - vec2(-size * 0.3, size * 0.1);
          float upperArmAngle = atan(upperArm.y, upperArm.x);
          float upperArmDist = length(upperArm);
          float upperArmMask = step(0.0, upperArmAngle) * step(upperArmAngle, 0.8) *
                              step(upperArmDist, size * 0.6) * step(size * 0.1, upperArmDist);

          // 下臂（向下弯曲）
          vec2 lowerArm = uv - vec2(-size * 0.3, -size * 0.1);
          float lowerArmAngle = atan(lowerArm.y, lowerArm.x);
          float lowerArmDist = length(lowerArm);
          float lowerArmMask = step(-0.8, lowerArmAngle) * step(lowerArmAngle, 0.0) *
                              step(lowerArmDist, size * 0.6) * step(size * 0.1, lowerArmDist);

          // 箭头尖端
          vec2 tip = uv - vec2(size * 0.2, 0.0);
          float tipMask = step(0.0, tip.x) * step(tip.x, size * 0.4);
          float tipShape = step(abs(tip.y), size * 0.25 - tip.x * 0.6);
          float arrowTip = tipMask * tipShape;

          return max(max(max(centerBody, upperArmMask), lowerArmMask), arrowTip);
        }

        void main() {
          vec2 uv = vUv;

          // 道路边缘检测（白色描边）
          float roadEdge = smoothstep(0.05, 0.1, uv.y) * smoothstep(0.05, 0.1, 1.0 - uv.y);
          float borderMask = 1.0 - smoothstep(0.1, 0.15, uv.y) * smoothstep(0.1, 0.15, 1.0 - uv.y);
          borderMask *= smoothstep(0.85, 0.9, uv.y) + smoothstep(0.85, 0.9, 1.0 - uv.y);

          // 主道路颜色
          vec3 roadColor = gaodeBlue;

          // 添加白色描边
          roadColor = mix(roadColor, white, borderMask);

          // 回旋镖箭头动画（更慢、更大、更清晰）
          float arrowSpeed = 0.3; // 箭头移动速度（大幅降低）
          float arrowSpacing = 0.25; // 箭头间距（增加）
          float arrowSize = 0.12; // 箭头大小（增大一倍）

          // 计算箭头位置
          float arrowProgress = mod(uv.x - time * arrowSpeed, arrowSpacing) / arrowSpacing;
          vec2 arrowCenter = vec2((arrowProgress - 0.5) * arrowSpacing * 6.0, (uv.y - 0.5) * 3.0);

          // 创建回旋镖箭头形状
          float arrow = createBoomerangArrow(arrowCenter, arrowSize);

          // 限制箭头在道路中央区域
          float centerMask = smoothstep(0.2, 0.3, uv.y) * smoothstep(0.2, 0.3, 1.0 - uv.y);
          arrow *= centerMask;

          // 箭头淡入淡出效果（更平滑）
          float arrowFade = smoothstep(0.0, 0.3, arrowProgress) * smoothstep(1.0, 0.7, arrowProgress);
          arrow *= arrowFade;

          // 增强箭头的可见性
          arrow = smoothstep(0.3, 0.7, arrow);

          // 混合回旋镖箭头颜色（更强的对比度）
          roadColor = mix(roadColor, arrowColor, arrow * 0.95);

          // 最终透明度
          float alpha = roadEdge;

          gl_FragColor = vec4(roadColor, alpha);
        }
      `,
      transparent: true,
      side: THREE.DoubleSide,
      depthWrite: false
    });
  }

  /**
   * 添加路径点标记
   * @param {THREE.Group} routeGroup - 路线组
   * @param {Array} pathPoints - 路径点数组
   */
  addPathMarkers(routeGroup, pathPoints) {
    console.log('添加路径点标记:', pathPoints);

    pathPoints.forEach((point, index) => {
      // 创建圆柱形标记，更适合地面道路
      const markerGeometry = new THREE.CylinderGeometry(3, 3, 1, 8);
      const markerMaterial = new THREE.MeshPhongMaterial({
        color: index === 0 ? 0x00ff00 : (index === pathPoints.length - 1 ? 0xff0000 : 0xffff00),
        transparent: true,
        opacity: 0.9
      });

      const marker = new THREE.Mesh(markerGeometry, markerMaterial);
      // 直接使用原始坐标，不做转换
      marker.position.set(point.x, 1, point.z);
      routeGroup.add(marker);

      console.log(`标记 ${index} 位置:`, {x: point.x, y: 1, z: point.z});

      // 添加标记文字（可选）
      if (index === 0 || index === pathPoints.length - 1) {
        const textGeometry = new THREE.RingGeometry(4, 6, 8);
        const textMaterial = new THREE.MeshBasicMaterial({
          color: index === 0 ? 0x00ff00 : 0xff0000,
          transparent: true,
          opacity: 0.6,
          side: THREE.DoubleSide
        });

        const textMarker = new THREE.Mesh(textGeometry, textMaterial);
        textMarker.position.set(point.x, 1.1, point.z);
        textMarker.rotation.x = -Math.PI / 2;
        routeGroup.add(textMarker);
      }
    });
  }

  /**
   * 显示指定设备的路线
   * @param {string} deviceId - 设备ID
   */
  showRoute(deviceId) {
    const route = this.routes.get(deviceId);
    if (route) {
      route.group.visible = true;
      route.visible = true;
      console.log(`显示设备 ${deviceId} 的路线`);
    }
  }

  /**
   * 隐藏指定设备的路线
   * @param {string} deviceId - 设备ID
   */
  hideRoute(deviceId) {
    const route = this.routes.get(deviceId);
    if (route) {
      route.group.visible = false;
      route.visible = false;
      console.log(`隐藏设备 ${deviceId} 的路线`);
    }
  }

  /**
   * 切换指定设备路线的显示状态
   * @param {string} deviceId - 设备ID
   * @returns {boolean} 当前显示状态
   */
  toggleRoute(deviceId) {
    const route = this.routes.get(deviceId);
    if (route) {
      if (route.visible) {
        this.hideRoute(deviceId);
      } else {
        this.showRoute(deviceId);
      }
      return route.visible;
    }
    return false;
  }

  /**
   * 获取指定设备路线的显示状态
   * @param {string} deviceId - 设备ID
   * @returns {boolean} 显示状态
   */
  isRouteVisible(deviceId) {
    const route = this.routes.get(deviceId);
    return route ? route.visible : false;
  }

  /**
   * 清除所有路线
   */
  clearAllRoutes() {
    this.routes.forEach((route, deviceId) => {
      this.scene.remove(route.group);
      
      // 清理几何体和材质
      route.group.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          if (child.geometry) child.geometry.dispose();
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach(material => material.dispose());
            } else {
              child.material.dispose();
            }
          }
        }
      });
    });
    
    this.routes.clear();
    console.log('所有路线已清除');
  }

  /**
   * 启动动画
   */
  startAnimation() {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
    }

    const animate = () => {
      const time = this.clock.getElapsedTime();

      // 更新所有路线材质的时间
      this.routes.forEach(route => {
        if (route.group && route.group.userData.routeMaterial) {
          route.group.userData.routeMaterial.uniforms.time.value = time;
        }
      });

      this.animationFrameId = requestAnimationFrame(animate);
    };

    animate();
  }

  /**
   * 停止动画
   */
  stopAnimation() {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  /**
   * 销毁路线渲染器
   */
  destroy() {
    this.stopAnimation();
    this.clearAllRoutes();
    console.log('路线渲染管理器已销毁');
  }

  /**
   * 获取所有设备ID列表
   * @returns {Array} 设备ID数组
   */
  getDeviceIds() {
    return Array.from(this.routes.keys());
  }
}
