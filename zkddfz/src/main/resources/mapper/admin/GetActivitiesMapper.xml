<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.baosight.api.feature.admin.route.get_activities.GetActivitiesMapper">
    
    <select id="query" parameterType="com.baosight.api.feature.admin.route.get_activities.GetActivitiesRequestBody" 
            resultType="com.baosight.api.feature.admin.route.get_activities.GetActivitiesResponseBody">
        SELECT 
            a.id, a.username, a.activity, a.created_at, 
            u.display_name
        FROM 
            bx_activity a
        LEFT OUTER JOIN 
            bx_user u ON a.username = u.username
        <where>
            <!-- 由于 activity 为倒排（a.id DESC），首次查询（pageParam = 0）不做限制 -->
            <if test="pageParam != null and pageParam != 0">
                AND a.id &lt; #{pageParam}
            </if>
            
            <if test="createdAtAfter != null">
                AND a.created_at &gt;= #{createdAtAfter}
            </if>
            
            <if test="createdAtBefore != null">
                AND a.created_at &lt;= #{createdAtBefore}
            </if>
            
            <if test="username != null">
                AND LOWER(a.username) LIKE CONCAT('%', LOWER(#{username}), '%')
            </if>
            
            <if test="displayName != null">
                AND LOWER(u.display_name) LIKE CONCAT('%', LOWER(#{displayName}), '%')
            </if>
            
            <if test="activity != null">
                AND LOWER(a.activity) LIKE CONCAT('%', LOWER(#{activity}), '%')
            </if>
        </where>
        
        ORDER BY a.id DESC
        
        LIMIT #{pageLimit}
    </select>
    
</mapper>
