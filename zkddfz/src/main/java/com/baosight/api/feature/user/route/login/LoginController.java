package com.baosight.api.feature.user.route.login;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baosight.api.constants.ApiPaths;
import com.baosight.api.db.activity.ActivityService;
import com.baosight.api.feature.user.security.AccessTokenService;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
public class LoginController {
  private final AccessTokenService accessTokenService;

  private final LoginService loginService;

  private final ActivityService activityService;

  @PostMapping(ApiPaths.User.LOGIN)
  public void handleRequest(@Valid @RequestBody LoginRequestBody requestBody, HttpServletResponse response) {
    loginService.checkUserCanLogin(requestBody);

    String username = requestBody.getUsername();
    Cookie cookie = accessTokenService.createAccessTokenCookie(username);
    response.addCookie(cookie);

    activityService.log(username, "账号登录");
  }
}
