<template>
  <a-config-provider
    :autoInsertSpaceInButton="false"
    :locale="zhCN"
    :theme="{
      algorithm: isDark ? theme.darkAlgorithm : theme.defaultAlgorithm,
    }"
  >
    <div
      v-if="!isPending"
      class="w-fit min-w-screen h-fit min-h-screen text-base text-neutral-950 dark:bg-neutral-950 dark:text-neutral-50"
    >
      <router-view />
      <auto-update />
    </div>
  </a-config-provider>
</template>

<script setup lang="ts">
import { useDark } from '@vueuse/core';
import { theme } from 'ant-design-vue';
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import { AutoUpdate, useAuth } from './views/app';
import { useColorScheme } from './views/app/use-color-scheme';

useColorScheme();

const { isPending } = useAuth();

const isDark = useDark();
</script>
