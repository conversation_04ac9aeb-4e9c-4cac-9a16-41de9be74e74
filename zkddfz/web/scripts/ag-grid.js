import fs from 'fs';

const updateAgGridEnterprise = () => {
  const filePath = 'node_modules/ag-grid-enterprise/dist/package/main.esm.mjs';
  const searchString = 'this.outputMissingLicenseKey';
  const replacementString = '// this.outputMissingLicenseKey';

  fs.readFile(filePath, 'utf8', (err, data) => {
    if (err) {
      return;
    }

    if (data.includes(replacementString)) {
      return;
    }

    const modifiedData = data.replace(new RegExp(searchString, 'g'), replacementString);

    fs.writeFile(filePath, modifiedData, 'utf8', err => {
      if (err) {
        return;
      }
    });
  });
};

const updateAgGridVue3 = () => {
  const filePath = 'node_modules/ag-grid-vue3/dist/package/main.esm.mjs';
  const startString = '/*! Bundled license information:';
  const endString = '*/';
  fs.readFile(filePath, 'utf8', (err, data) => {
    if (err) {
      return;
    }

    const rows = data.split('\n');
    const startLineNo = rows.findIndex(row => row === startString);
    if (startLineNo !== -1) {
      const linesToRemove = [];
      let currentRowNo = startLineNo;
      while (true) {
        if (rows[currentRowNo] !== endString) {
          linesToRemove.unshift(currentRowNo);
          currentRowNo++;
        } else {
          linesToRemove.unshift(currentRowNo);
          break;
        }
      }

      for (const lineNo of linesToRemove) {
        rows.splice(lineNo, 1);
      }

      fs.writeFile(filePath, rows.join('\n'), 'utf8', err => {
        if (err) {
          return;
        }
      });
    }
  });
};

updateAgGridEnterprise();

updateAgGridVue3();
