import { api } from '@/common/api';
import type { ConditionMap } from '@/common/types';
import { dateFilterParams, dateTimeValueFormatter } from '@/components/ag-grid';
import { ApiPaths } from '@/constants/api-paths';
import type { Ref } from 'vue';
import type { Activity, ActivityDTO } from './types';

export const getData = async (
  conditionMap: Ref<ConditionMap | undefined>,
  pageParam: number,
  pageLimit: number,
): Promise<Activity[]> => {
  const payload = { ...conditionMap.value, pageParam, pageLimit };

  const { data } = await api.post<ActivityDTO[]>(ApiPaths.Admin.GET_ACTIVITIES, payload);

  return data.map((v, i) => ({ rowNumber: i + 1, ...v }));
};

export const createColumnDefs = () => [
  { headerName: '序号', field: 'rowNumber', filter: 'agNumberColumnFilter', maxWidth: 128 },

  { headerName: '操作人员账号', field: 'username', maxWidth: 176 },

  { headerName: '操作人员姓名', field: 'displayName', maxWidth: 176 },

  {
    headerName: '操作时间',
    field: 'createdAt',
    filter: 'agDateColumnFilter',
    filterParams: dateFilterParams,
    valueFormatter: dateTimeValueFormatter,
    maxWidth: 176,
  },

  { headerName: '操作记录', field: 'activity', filter: 'agTextColumnFilter' },
];
