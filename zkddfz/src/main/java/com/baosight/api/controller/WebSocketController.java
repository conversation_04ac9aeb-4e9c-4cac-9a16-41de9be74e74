package com.baosight.api.controller;

import com.baosight.api.websocket.RouteWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * WebSocket 管理控制器
 * 提供 WebSocket 服务的管理接口
 */
@RestController
@RequestMapping("/api/websocket")
@CrossOrigin(origins = "*")
public class WebSocketController {

    @Autowired
    private RouteWebSocketHandler routeWebSocketHandler;

    /**
     * 获取 WebSocket 连接状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        int connectionCount = routeWebSocketHandler.getConnectionCount();

        Map<String, Object> status = Map.of(
            "status", "running",
            "connectionCount", connectionCount,
            "endpoint", "/route",
            "timestamp", System.currentTimeMillis(),
            "message", "WebSocket 服务正在运行"
        );

        return ResponseEntity.ok(status);
    }

    /**
     * 获取当前模拟车辆数据
     */
    @GetMapping("/vehicles")
    public ResponseEntity<Map<String, Object>> getVehicles() {
        try {
            var vehicles = routeWebSocketHandler.getSimulatedVehicles();

            Map<String, Object> response = Map.of(
                "vehicles", vehicles,
                "count", vehicles.size(),
                "timestamp", System.currentTimeMillis()
            );

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = Map.of(
                "success", false,
                "message", "获取车辆数据失败: " + e.getMessage(),
                "timestamp", System.currentTimeMillis()
            );
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 手动广播车辆控制电文
     */
    @PostMapping("/broadcast")
    public ResponseEntity<Map<String, Object>> broadcastVehicleCommands() {
        try {
            routeWebSocketHandler.broadcastVehicleCommands();

            Map<String, Object> response = Map.of(
                "success", true,
                "message", "车辆控制电文已广播",
                "connectionCount", routeWebSocketHandler.getConnectionCount(),
                "timestamp", System.currentTimeMillis()
            );

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = Map.of(
                "success", false,
                "message", "广播失败: " + e.getMessage(),
                "timestamp", System.currentTimeMillis()
            );

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 广播自定义消息到所有连接的客户端
     */
    @PostMapping("/broadcast-message")
    public ResponseEntity<Map<String, Object>> broadcastMessage(@RequestBody Map<String, Object> message) {
        try {
            routeWebSocketHandler.broadcastMessage(message);

            Map<String, Object> response = Map.of(
                "success", true,
                "message", "消息已广播到所有客户端",
                "connectionCount", routeWebSocketHandler.getConnectionCount(),
                "timestamp", System.currentTimeMillis()
            );

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = Map.of(
                "success", false,
                "message", "广播消息失败: " + e.getMessage(),
                "timestamp", System.currentTimeMillis()
            );

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 更新指定车辆的数据
     */
    @PostMapping("/vehicles/{deviceId}")
    public ResponseEntity<Map<String, Object>> updateVehicle(
            @PathVariable String deviceId,
            @RequestParam double x,
            @RequestParam double z,
            @RequestParam(defaultValue = "0") double rotationY,
            @RequestParam(defaultValue = "100") int power,
            @RequestParam(defaultValue = "0") int consume) {

        try {
            routeWebSocketHandler.updateVehicleData(deviceId, x, z, rotationY, power, consume);

            Map<String, Object> response = Map.of(
                "success", true,
                "message", "车辆数据已更新",
                "deviceId", deviceId,
                "position", Map.of("x", x, "z", z),
                "rotation", Map.of("y", rotationY),
                "power", power,
                "consume", consume,
                "timestamp", System.currentTimeMillis()
            );

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = Map.of(
                "success", false,
                "message", "更新失败: " + e.getMessage(),
                "timestamp", System.currentTimeMillis()
            );

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 发送测试消息
     */
    @PostMapping("/test")
    public ResponseEntity<Map<String, Object>> sendTestMessage(@RequestParam(defaultValue = "Hello WebSocket") String message) {
        try {
            Map<String, Object> testMessage = Map.of(
                "type", "test",
                "action", "broadcast",
                "data", Map.of(
                    "message", message,
                    "timestamp", System.currentTimeMillis(),
                    "source", "REST API"
                )
            );

            routeWebSocketHandler.broadcastMessage(testMessage);

            Map<String, Object> response = Map.of(
                "success", true,
                "message", "测试消息已发送",
                "content", message,
                "connectionCount", routeWebSocketHandler.getConnectionCount(),
                "timestamp", System.currentTimeMillis()
            );

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = Map.of(
                "success", false,
                "message", "发送失败: " + e.getMessage(),
                "timestamp", System.currentTimeMillis()
            );

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 发送测试路线数据
     */
    @PostMapping("/test-route")
    public ResponseEntity<Map<String, Object>> sendTestRoute() {
        try {
            // 创建测试路线数据
            Map<String, Object> testRoute = Map.of(
                "type", "route_data",
                "action", "batch",
                "data", createTestRouteData()
            );
            
            routeWebSocketHandler.broadcastMessage(testRoute);
            
            Map<String, Object> response = Map.of(
                "success", true,
                "message", "测试路线数据已发送",
                "connectionCount", routeWebSocketHandler.getConnectionCount()
            );
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = Map.of(
                "success", false,
                "message", "发送测试路线失败: " + e.getMessage()
            );
            
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 创建测试路线数据
     */
    private Object createTestRouteData() {
        return java.util.List.of(
            Map.of(
                "timestamp", System.currentTimeMillis(),
                "position", Map.of("x", 0, "y", 0, "z", 0),
                "rotation", Map.of("x", 0, "y", 0, "z", 0),
                "duration", 2000,
                "speed", 1.0,
                "battery", 85
            ),
            Map.of(
                "timestamp", System.currentTimeMillis() + 2000,
                "position", Map.of("x", 20, "y", 0, "z", 0),
                "rotation", Map.of("x", 0, "y", 90, "z", 0),
                "duration", 2000,
                "speed", 1.0,
                "battery", 83
            ),
            Map.of(
                "timestamp", System.currentTimeMillis() + 4000,
                "position", Map.of("x", 20, "y", 0, "z", 20),
                "rotation", Map.of("x", 0, "y", 180, "z", 0),
                "duration", 2000,
                "speed", 1.0,
                "battery", 81
            ),
            Map.of(
                "timestamp", System.currentTimeMillis() + 6000,
                "position", Map.of("x", 0, "y", 0, "z", 20),
                "rotation", Map.of("x", 0, "y", 270, "z", 0),
                "duration", 2000,
                "speed", 1.0,
                "battery", 79
            ),
            Map.of(
                "timestamp", System.currentTimeMillis() + 8000,
                "position", Map.of("x", 0, "y", 0, "z", 0),
                "rotation", Map.of("x", 0, "y", 0, "z", 0),
                "duration", 2000,
                "speed", 1.0,
                "battery", 77
            )
        );
    }
}
