<script setup lang="ts">
import { computed, ref, watch, onMounted, onUnmounted } from 'vue';
import * as THREE from 'three';

interface ModelData {
  id: string;
  name: string;
  currentPosition?: { x: number; y: number; z: number };
  currentRotation?: { x: number; y: number; z: number };
  battery?: number;
  type?: string;
  path?: string;
}

interface Props {
  model: ModelData;
  sceneManagerRef?: any;
  visible?: boolean;
  screenPosition?: { x: number; y: number; visible: boolean };
}

const props = defineProps<Props>();

// 设备类型映射
const deviceTypeMap = {
  '/models/桥式转载机ok.glb': '桥式转载机',
  '/models/大型布料机ok-6.glb': '大型布料机',
  '/models/中继转载机-B-ok.glb': '中继转载机',
  '/models/扇形布料机ok.glb': '扇形布料机',
  '/models/3500T斗轮挖掘机.glb': '3500T斗轮挖掘机',
  '/models/1500T斗轮挖掘机.glb': '1500T斗轮挖掘机',
  '/models/移动转载机ok.glb': '移动转载机',
};

// 计算设备类型
const deviceType = computed(() => {
  if (props.model.path && deviceTypeMap[props.model.path]) {
    return deviceTypeMap[props.model.path];
  }
  return props.model.type || '未知设备';
});

// 计算电量状态
const batteryStatus = computed(() => {
  const battery = props.model.battery || 0;
  if (battery < 30) return { text: '低电量', color: 'text-red-400', bg: 'bg-red-500/20' };
  if (battery < 60) return { text: '中等', color: 'text-yellow-400', bg: 'bg-yellow-500/20' };
  return { text: '充足', color: 'text-green-400', bg: 'bg-green-500/20' };
});

// 格式化坐标
const formatCoordinate = (value: number) => {
  return value?.toFixed(2) || '0.00';
};

// 格式化角度
const formatAngle = (value: number) => {
  return value?.toFixed(1) || '0.0';
};

// 提取车辆编号
const vehicleNumber = computed(() => {
  const id = props.model.id;
  // 提取ID中的数字部分
  const match = id.match(/\d+/);
  return match ? match[0] : id;
});
</script>

<template>
  <div
    v-if="visible && screenPosition?.visible"
    class="model-info-card fixed pointer-events-none z-50"
    :style="{
      left: `${screenPosition.x}px`,
      top: `${screenPosition.y}px`,
      transform: 'translate(-50%, -100%)'
    }"
  >
    <!-- 卡片主体 -->
    <div
      class="bg-slate-800/90 backdrop-blur-sm border border-cyan-400/30 rounded-lg shadow-lg shadow-cyan-500/20 p-2"
      style="clip-path: polygon(0.3rem 0, 100% 0, 100% calc(100% - 0.3rem), calc(100% - 0.3rem) 100%, 0 100%, 0 0.3rem); width: 240px; height: 120px;"
    >
      <!-- 卡片头部 -->
      <div class="flex items-center justify-between mb-1 pb-1 border-b border-cyan-400/20">
        <div class="flex items-center gap-1">
          <div class="w-1.5 h-1.5 rounded-full bg-cyan-400 shadow-lg shadow-cyan-400/50 animate-pulse"></div>
          <h3 class="text-xs font-semibold text-white truncate max-w-[120px]">{{ model.name }}</h3>
        </div>
        <div class="text-xs text-cyan-400 font-mono">#{{ vehicleNumber }}</div>
      </div>

      <!-- 卡片内容 -->
      <div class="space-y-1 text-xs overflow-hidden">
        <!-- 第一行：设备类型和电量 -->
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-1">
            <span class="text-slate-400 text-xs">类型:</span>
            <span class="text-cyan-300 text-xs font-medium truncate max-w-[80px]">{{ deviceType }}</span>
          </div>
          <div class="flex items-center gap-1">
            <div class="w-8 h-1 bg-slate-700 rounded-full overflow-hidden">
              <div
                class="h-full transition-all duration-300"
                :class="batteryStatus.bg"
                :style="{ width: `${model.battery || 0}%` }"
              ></div>
            </div>
            <span :class="batteryStatus.color" class="font-mono text-xs">{{ model.battery || 0 }}%</span>
          </div>
        </div>

        <!-- 第二行：坐标信息 -->
        <div class="flex items-center justify-between">
          <span class="text-slate-400 text-xs">坐标:</span>
          <div class="flex items-center gap-1 text-xs">
            <span class="text-white font-mono">{{ formatCoordinate(model.currentPosition?.x) }}</span>
            <span class="text-slate-500">,</span>
            <span class="text-white font-mono">{{ formatCoordinate(model.currentPosition?.y) }}</span>
            <span class="text-slate-500">,</span>
            <span class="text-white font-mono">{{ formatCoordinate(model.currentPosition?.z) }}</span>
          </div>
        </div>

        <!-- 第三行：角度信息 -->
        <div class="flex items-center justify-between">
          <span class="text-slate-400 text-xs">角度:</span>
          <div class="flex items-center gap-1 text-xs">
            <span class="text-white font-mono">{{ formatAngle(model.currentRotation?.x) }}°</span>
            <span class="text-slate-500">,</span>
            <span class="text-white font-mono">{{ formatAngle(model.currentRotation?.y) }}°</span>
            <span class="text-slate-500">,</span>
            <span class="text-white font-mono">{{ formatAngle(model.currentRotation?.z) }}°</span>
          </div>
        </div>

        <!-- 第四行：状态指示 -->
        <div class="flex items-center justify-between">
          <span class="text-slate-400 text-xs">状态:</span>
          <div class="flex items-center gap-1">
            <div class="w-1 h-1 rounded-full bg-green-400 animate-pulse"></div>
            <span class="text-green-400 text-xs">在线</span>
          </div>
        </div>
      </div>

      <!-- 卡片底部装饰 -->
      <div class="absolute bottom-0.5 left-0.5 w-2 h-2 border-l border-b border-cyan-400/40"></div>
      <div class="absolute top-0.5 right-0.5 w-2 h-2 border-r border-t border-cyan-400/40"></div>
    </div>

    <!-- 指向线 -->
    <div class="absolute top-full left-1/2 transform -translate-x-1/2">
      <div class="w-0.5 h-3 bg-gradient-to-b from-cyan-400/60 to-transparent"></div>
      <div class="w-1.5 h-1.5 bg-cyan-400/60 rounded-full transform -translate-x-1/2 -translate-y-0.5"></div>
    </div>
  </div>
</template>

<style scoped>
.model-info-card {
  /* 确保卡片始终朝向相机 */
  transform-style: preserve-3d;
  /* 添加平滑过渡效果 */
  transition: all 0.1s ease-out;
  /* 确保卡片在最顶层 */
  z-index: 9999;
}

/* 卡片进入动画 */
.model-info-card {
  animation: cardFadeIn 0.3s ease-out;
}

@keyframes cardFadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -100%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -100%) scale(1);
  }
}

/* 自定义滚动条 */
.model-info-card::-webkit-scrollbar {
  width: 2px;
}

.model-info-card::-webkit-scrollbar-track {
  background: rgba(51, 65, 85, 0.3);
}

.model-info-card::-webkit-scrollbar-thumb {
  background: rgba(6, 182, 212, 0.5);
  border-radius: 1px;
}

/* 确保卡片内容清晰可见 */
.model-info-card .bg-slate-800\/90 {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}
</style>
