package com.baosight.api.feature.admin.route.update_user_roles;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baosight.api.constants.ApiPaths;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
public class UpdateUserRolesController {
  private final UpdateUserRolesService updateUserRolesService;

  @PostMapping(ApiPaths.Admin.UPDATE_USER_ROLES)
  public void handleRequest(@Valid @RequestBody UpdateUserRolesRequestBody requestBody) {
    updateUserRolesService.updateUserRoles(requestBody);
  }
}
