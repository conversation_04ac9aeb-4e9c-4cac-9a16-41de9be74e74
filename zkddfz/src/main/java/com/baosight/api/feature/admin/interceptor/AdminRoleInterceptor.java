package com.baosight.api.feature.admin.interceptor;

import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.servlet.HandlerInterceptor;

import com.baosight.api.db.role.RoleNameEnums;
import com.baosight.api.db.user.UserEntity;
import com.baosight.api.db.user_role.UserRoleRepository;
import com.baosight.api.feature.user.security.UserContextService;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class AdminRoleInterceptor implements HandlerInterceptor {
  private final UserContextService userContextService;

  private final UserRoleRepository userRoleRepository;

  private boolean isAdmin(String username) {
    return userRoleRepository.existsByUsernameAndRoleName(username, RoleNameEnums.管理员);
  }

  @Override
  public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
      @NonNull Object handler)
      throws Exception {
    UserEntity user = userContextService.getUserFromRequest(request);

    if (user == null) {
      throw new ResponseStatusException(HttpStatus.UNAUTHORIZED);
    }

    if (!isAdmin(user.getUsername())) {
      throw new ResponseStatusException(HttpStatus.UNAUTHORIZED);
    }

    return true;
  }
}
