<template>
  <div v-if="isAdmin" class="min-w-256 flex">
    <side-menu />
    <router-view />
  </div>
  <div v-else>
    <Error401 />
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/user';
import Error401 from '@/views/sys/error-401.vue';
import { storeToRefs } from 'pinia';
import SideMenu from './side-menu.vue';

const userStore = useUserStore();
const { isAdmin } = storeToRefs(userStore);
</script>
