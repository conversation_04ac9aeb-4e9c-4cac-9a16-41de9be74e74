<script setup lang="ts">
import { computed, ref, watch } from 'vue';

interface DeviceType {
  id: string;
  name: string;
  path: string;
  scale: number;
  typeCode: string; // 新增车辆类型代码
  defaultMaterial: {
    color: number;
    roughness: number;
    metalness: number;
  };
}

interface Props {
  visible: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits(['close', 'confirm']);

// 九种车型配置
const deviceTypes: DeviceType[] = [
  {
    id: 'mobile_loader',
    name: '移动转载机',
    path: '/models/new/移动转载机ok.glb',
    scale: 0.01,
    typeCode: 'MBC',
    defaultMaterial: { color: 0x02536c, roughness: 0.2, metalness: 0.9 }
  },
  {
    id: 'bridge_loader',
    name: '桥式转载机',
    path: '/models/new/桥式转载机ok.glb',
    scale: 0.01,
    typeCode: 'BBC',
    defaultMaterial: { color: 0x6c0202, roughness: 0.2, metalness: 0.9 }
  },
  {
    id: 'relay_loader',
    name: '中继转载机',
    path: '/models/new/中继转载机-B-ok.glb',
    scale: 0.01,
    typeCode: 'TBC',
    defaultMaterial: { color: 0x3a6c02, roughness: 0.2, metalness: 0.9 }
  },
  {
    id: 'excavator_3500t',
    name: '3500T斗轮挖掘机',
    path: '/models/new/3500T斗轮挖掘机.glb',
    scale: 0.01,
    typeCode: 'BWE',
    defaultMaterial: { color: 0x02536c, roughness: 0.2, metalness: 0.9 }
  },
  {
    id: 'large_spreader',
    name: '大型布料机',
    path: '/models/new/大型布料机ok-6.glb',
    scale: 0.01,
    typeCode: 'RSC',
    defaultMaterial: { color: 0x02536c, roughness: 0.2, metalness: 0.9 }
  },
  {
    id: 'mobile_power_vehicle',
    name: '移动供电车',
    path: '/models/new/移动供电车ok.glb',
    scale: 0.01,
    typeCode: 'PCC',
    defaultMaterial: { color: 0x6c4502, roughness: 0.2, metalness: 0.9 }
  },
  {
    id: 'fan_spreader',
    name: '扇形布料机',
    path: '/models/new/扇形布料机ok.glb',
    scale: 0.01,
    typeCode: 'RSCM',
    defaultMaterial: { color: 0x0f6c02, roughness: 0.2, metalness: 0.9 }
  },
  {
    id: 'excavator_1500t',
    name: '1500T斗轮挖掘机',
    path: '/models/new/1500T斗轮挖掘机.glb',
    scale: 0.01,
    typeCode: 'BWEM',
    defaultMaterial: { color: 0x02536c, roughness: 0.3, metalness: 0.8 }
  },
  {
    id: 'mobile_distribution_funnel',
    name: '移动分料漏斗',
    path: '/models/new/移动转载机ok.glb',
    scale: 0.01,
    typeCode: 'MDF',
    defaultMaterial: { color: 0x4a0f6c, roughness: 0.2, metalness: 0.9 }
  }
];

// 表单数据
const formData = ref({
  deviceType: '',
  deviceName: '',
  initialPosition: { x: 0, y: 0, z: 0 },
  initialRotation: { x: 0, y: 0, z: 0 },
  currentPosition: { x: 0, y: 0, z: 0 },
  currentRotation: { x: 0, y: 0, z: 0 },
  material: {
    color: '#02536c',
    roughness: 0.2,
    metalness: 0.9
  },
  battery: 85,
  // 新增目标坐标设置
  hasTargetCoordinate: false,
  targetPosition: { x: 0, y: 0, z: 0 }
});

// 计算属性
const selectedType = computed(() => 
  deviceTypes.find(type => type.id === formData.value.deviceType)
);

// 生成唯一ID - 使用车辆类型代码和随机五位数字
const generateDeviceId = (deviceTypeId: string) => {
  const deviceType = deviceTypes.find(type => type.id === deviceTypeId);
  if (!deviceType) {
    throw new Error(`未找到设备类型: ${deviceTypeId}`);
  }

  const randomFiveDigits = Math.floor(Math.random() * 90000) + 10000; // 生成10000-99999的随机数
  return `${deviceType.typeCode}_${randomFiveDigits}`;
};

// 获取设备类型代码 - 现在直接从设备类型配置中获取
const getDeviceTypeCode = (deviceTypeId: string) => {
  const deviceType = deviceTypes.find(type => type.id === deviceTypeId);
  return deviceType?.typeCode || 'MBC'; // 默认为移动转载机
};

// 调用添加设备API
const callAddDeviceAPI = async (deviceData) => {
  try {
    console.log('调用添加设备API:', deviceData);

    const response = await fetch('http://localhost:5001/add_device', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(deviceData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('添加设备API调用成功:', result);
    return result;
  } catch (error) {
    console.error('添加设备API调用失败:', error);
    throw error;
  }
};

// 重置表单
const resetForm = () => {
  formData.value = {
    deviceType: '',
    deviceName: '',
    initialPosition: { x: 0, y: 0, z: 0 },
    initialRotation: { x: 0, y: 0, z: 0 },
    currentPosition: { x: 0, y: 0, z: 0 },
    currentRotation: { x: 0, y: 0, z: 0 },
    material: {
      color: '#02536c',
      roughness: 0.2,
      metalness: 0.9
    },
    battery: 85,
    hasTargetCoordinate: false,
    targetPosition: { x: 0, y: 0, z: 0 }
  };
};

// 监听设备类型变化，自动填充默认值
watch(() => formData.value.deviceType, (newType) => {
  if (newType) {
    const type = deviceTypes.find(t => t.id === newType);
    if (type) {
      formData.value.deviceName = type.name;
      const hexColor = `#${type.defaultMaterial.color.toString(16).padStart(6, '0')}`;
      formData.value.material = {
        color: hexColor,
        roughness: type.defaultMaterial.roughness,
        metalness: type.defaultMaterial.metalness
      };
      // 同步初始位置到当前位置
      formData.value.currentPosition = { ...formData.value.initialPosition };
      formData.value.currentRotation = { ...formData.value.initialRotation };
    }
  }
});

// 监听位置变化，同步到当前位置
watch(() => formData.value.initialPosition, (newPos) => {
  formData.value.currentPosition = { ...newPos };
}, { deep: true });

watch(() => formData.value.initialRotation, (newRot) => {
  formData.value.currentRotation = { ...newRot };
}, { deep: true });

// 关闭弹框
const handleClose = () => {
  resetForm();
  emit('close');
};

// 确认添加
const handleConfirm = async () => {
  if (!selectedType.value || !formData.value.deviceName.trim()) {
    alert('请选择设备类型并输入设备名称');
    return;
  }

  // const deviceId = generateDeviceId(formData.value.deviceType);

  const deviceId = "device_26482"
  // 转换颜色值
  const colorHex = formData.value.material.color.replace('#', '');
  const colorValue = parseInt(colorHex, 16);

  // 构建与现有models数组结构一致的数据
  const newModel = {
    id: deviceId,
    name: formData.value.deviceName.trim(),
    path: selectedType.value.path,
    initialPosition: { ...formData.value.initialPosition },
    initialRotation: { ...formData.value.initialRotation },
    currentPosition: { ...formData.value.currentPosition },
    currentRotation: { ...formData.value.currentRotation },
    scale: selectedType.value.scale,
    defaultAnimation: 0,
    movementSpeed: 30.0,
    autoGroundPosition: true,
    material: {
      color: colorValue,
      roughness: formData.value.material.roughness,
      metalness: formData.value.material.metalness,
    },
    battery: formData.value.battery,
    consume: 0, // 初始累计耗电量
    distance: 0, // 初始累计位移距离
    sensors: [],
    deviceType: formData.value.deviceType, // 添加设备类型字段
    type: selectedType.value.typeCode // 添加车辆类型代码字段
  };

  // 如果设置了目标坐标，调用API
  if (formData.value.hasTargetCoordinate) {
    try {
      const apiData = {
        device_id: deviceId,
        device_start: {
          x: formData.value.initialPosition.x,
          y: formData.value.initialPosition.y,
          z: formData.value.initialPosition.z
        },
        device_end: {
          x: formData.value.targetPosition.x,
          y: formData.value.targetPosition.y,
          z: formData.value.targetPosition.z
        },
        device_rotation: {
          x: formData.value.initialRotation.x,
          y: formData.value.initialRotation.y,
          z: formData.value.initialRotation.z
        },
        device_type: getDeviceTypeCode(formData.value.deviceType)
      };

      await callAddDeviceAPI(apiData);
      console.log('✅ 设备添加API调用成功');
    } catch (error) {
      console.error('设备添加API调用失败:', error);
      alert(`设备添加API调用失败: ${error.message}`);
      return; // 如果API调用失败，不继续添加设备
    }
  }

  emit('confirm', newModel);
  resetForm();
  emit('close');
};

// 监听弹框显示状态，重置表单
watch(() => props.visible, (visible) => {
  if (visible) {
    resetForm();
  }
});
</script>

<template>
  <!-- 弹框遮罩 -->
  <div
    v-if="visible"
    class="fixed inset-0 bg-black/50 backdrop-blur-sm z-101 flex items-center justify-center"
    @click.self="handleClose"
  >
    <!-- 弹框内容 -->
    <div
      class="bg-slate-800/95 border border-cyan-500/30 rounded-lg shadow-2xl shadow-cyan-500/20 w-full max-w-2xl max-h-[90vh] overflow-y-auto"
      style="clip-path: polygon(1rem 0, 100% 0, 100% calc(100% - 1rem), calc(100% - 1rem) 100%, 0 100%, 0 1rem)"
    >
      <!-- 弹框头部 -->
      <div class="flex items-center justify-between p-6 border-b border-cyan-500/20">
        <h2 class="text-xl font-bold text-white">添加设备</h2>
        <button
          @click="handleClose"
          class="text-slate-400 hover:text-white transition-colors"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>

      <!-- 弹框内容 -->
      <div class="p-6 space-y-6">
        <!-- 设备类型选择 -->
        <div>
          <label class="block text-sm font-medium text-white mb-2">设备类型 *</label>
          <select
            v-model="formData.deviceType"
            class="w-full px-3 py-2 bg-slate-700/60 border border-cyan-500/30 text-white rounded focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200"
          >
            <option value="" disabled>请选择设备类型</option>
            <option v-for="type in deviceTypes" :key="type.id" :value="type.id">
              {{ type.name }}
            </option>
          </select>
        </div>

        <!-- 设备名称 -->
        <div>
          <label class="block text-sm font-medium text-white mb-2">设备名称 *</label>
          <input
            v-model="formData.deviceName"
            type="text"
            placeholder="输入设备名称"
            class="w-full px-3 py-2 bg-slate-700/60 border border-cyan-500/30 text-white rounded placeholder-slate-400 focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200"
          />
        </div>

        <!-- 初始位置 -->
        <div>
          <label class="block text-sm font-medium text-white mb-2">初始位置</label>
          <div class="grid grid-cols-3 gap-4">
            <div>
              <label class="block text-xs text-slate-400 mb-1">X (左右)</label>
              <input
                v-model.number="formData.initialPosition.x"
                type="number"
                step="0.1"
                class="w-full px-3 py-2 bg-slate-700/60 border border-cyan-500/30 text-white rounded focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200"
              />
            </div>
            <div>
              <label class="block text-xs text-slate-400 mb-1">Y (高度)</label>
              <input
                v-model.number="formData.initialPosition.y"
                type="number"
                step="0.1"
                class="w-full px-3 py-2 bg-slate-700/60 border border-cyan-500/30 text-white rounded focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200"
              />
            </div>
            <div>
              <label class="block text-xs text-slate-400 mb-1">Z (前后)</label>
              <input
                v-model.number="formData.initialPosition.z"
                type="number"
                step="0.1"
                class="w-full px-3 py-2 bg-slate-700/60 border border-cyan-500/30 text-white rounded focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200"
              />
            </div>
          </div>
        </div>

        <!-- 初始旋转角度 -->
        <div>
          <label class="block text-sm font-medium text-white mb-2">初始旋转角度 (度)</label>
          <div class="grid grid-cols-3 gap-4">
            <div>
              <label class="block text-xs text-slate-400 mb-1">X</label>
              <input
                v-model.number="formData.initialRotation.x"
                type="number"
                step="1"
                min="-360"
                max="360"
                class="w-full px-3 py-2 bg-slate-700/60 border border-cyan-500/30 text-white rounded focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200"
              />
            </div>
            <div>
              <label class="block text-xs text-slate-400 mb-1">Y</label>
              <input
                v-model.number="formData.initialRotation.y"
                type="number"
                step="1"
                min="-360"
                max="360"
                class="w-full px-3 py-2 bg-slate-700/60 border border-cyan-500/30 text-white rounded focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200"
              />
            </div>
            <div>
              <label class="block text-xs text-slate-400 mb-1">Z</label>
              <input
                v-model.number="formData.initialRotation.z"
                type="number"
                step="1"
                min="-360"
                max="360"
                class="w-full px-3 py-2 bg-slate-700/60 border border-cyan-500/30 text-white rounded focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200"
              />
            </div>
          </div>
        </div>

        <!-- 材质配置 -->
        <div>
          <label class="block text-sm font-medium text-white mb-2">材质配置</label>
          <div class="space-y-4">
            <div>
              <label class="block text-xs text-slate-400 mb-1">颜色</label>
              <input
                v-model="formData.material.color"
                type="color"
                class="w-full h-10 bg-slate-700/60 border border-cyan-500/30 rounded cursor-pointer"
              />
            </div>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-xs text-slate-400 mb-1">粗糙度 ({{ formData.material.roughness }})</label>
                <input
                  v-model.number="formData.material.roughness"
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  class="w-full h-2 bg-slate-700/60 rounded-sm outline-none appearance-none slider"
                />
              </div>
              <div>
                <label class="block text-xs text-slate-400 mb-1">金属度 ({{ formData.material.metalness }})</label>
                <input
                  v-model.number="formData.material.metalness"
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  class="w-full h-2 bg-slate-700/60 rounded-sm outline-none appearance-none slider"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 电量设置 -->
        <div>
          <label class="block text-sm font-medium text-white mb-2">初始电量 ({{ formData.battery }}%)</label>
          <input
            v-model.number="formData.battery"
            type="range"
            min="0"
            max="100"
            step="5"
            class="w-full h-2 bg-slate-700/60 rounded-sm outline-none appearance-none slider"
          />
          <div class="flex justify-between text-xs text-slate-400 mt-1">
            <span>0%</span>
            <span>50%</span>
            <span>100%</span>
          </div>
        </div>

        <!-- 目标坐标设置 -->
        <div>
          <div class="flex items-center mb-4">
            <input
              v-model="formData.hasTargetCoordinate"
              type="checkbox"
              id="hasTargetCoordinate"
              class="w-4 h-4 text-cyan-600 bg-slate-700/60 border-cyan-500/30 rounded focus:ring-cyan-500 focus:ring-2"
            />
            <label for="hasTargetCoordinate" class="ml-2 text-sm font-medium text-white">
              设置目标坐标 (将调用路径规划API)
            </label>
          </div>

          <div v-if="formData.hasTargetCoordinate" class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-white mb-2">目标位置</label>
              <div class="grid grid-cols-3 gap-4">
                <div>
                  <label class="block text-xs text-slate-400 mb-1">X (左右)</label>
                  <input
                    v-model.number="formData.targetPosition.x"
                    type="number"
                    step="0.1"
                    class="w-full px-3 py-2 bg-slate-700/60 border border-cyan-500/30 text-white rounded focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200"
                  />
                </div>
                <div>
                  <label class="block text-xs text-slate-400 mb-1">Y (高度)</label>
                  <input
                    v-model.number="formData.targetPosition.y"
                    type="number"
                    step="0.1"
                    class="w-full px-3 py-2 bg-slate-700/60 border border-cyan-500/30 text-white rounded focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200"
                  />
                </div>
                <div>
                  <label class="block text-xs text-slate-400 mb-1">Z (前后)</label>
                  <input
                    v-model.number="formData.targetPosition.z"
                    type="number"
                    step="0.1"
                    class="w-full px-3 py-2 bg-slate-700/60 border border-cyan-500/30 text-white rounded focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200"
                  />
                </div>
              </div>
            </div>

            <div class="p-3 bg-cyan-900/20 border border-cyan-500/30 rounded">
              <div class="flex items-start">
                <svg class="w-5 h-5 text-cyan-400 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                </svg>
                <div class="text-xs text-cyan-300">
                  <p class="font-medium mb-1">API调用说明：</p>
                  <p>勾选此选项后，添加设备时将自动调用 <code class="bg-slate-800 px-1 rounded">http://localhost:5001/add_device</code> 接口</p>
                  <p class="mt-1">传递起始坐标、目标坐标、旋转角度和设备类型信息</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 弹框底部 -->
      <div class="flex items-center justify-end gap-4 p-6 border-t border-cyan-500/20">
        <button
          @click="handleClose"
          class="px-6 py-2 bg-slate-600/60 border border-slate-500 text-slate-300 rounded hover:bg-slate-600/80 transition-all duration-200"
        >
          取消
        </button>
        <button
          @click="handleConfirm"
          :disabled="!formData.deviceType || !formData.deviceName.trim()"
          class="px-6 py-2 bg-cyan-600/60 border border-cyan-500 text-cyan-300 rounded hover:bg-cyan-600/80 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
        >
          确认添加
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 自定义滑块样式 */
.slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  background: #06b6d4;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 8px rgba(6, 182, 212, 0.5);
}

.slider::-webkit-slider-track {
  background: rgba(51, 65, 85, 0.6);
  height: 8px;
  border-radius: 4px;
}

.slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #06b6d4;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 8px rgba(6, 182, 212, 0.5);
}

.slider::-moz-range-track {
  background: rgba(51, 65, 85, 0.6);
  height: 8px;
  border-radius: 4px;
  border: none;
}
</style>
