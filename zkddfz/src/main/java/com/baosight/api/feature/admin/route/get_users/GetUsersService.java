package com.baosight.api.feature.admin.route.get_users;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.baosight.api.db.role.RoleNameEnums;
import com.baosight.api.db.user.UserEntity;
import com.baosight.api.db.user_role.UserRoleEntity;
import com.baosight.api.db.user_role.UserRoleRepository;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class GetUsersService {
  private final GetUsersMapper getUsersMapper;

  private final UserRoleRepository userRoleRepository;

  private GetUsersResponseBody mapEntityToResponse(UserEntity userEntity, List<RoleNameEnums> roles) {
    GetUsersResponseBody responseBodyItem = new GetUsersResponseBody();
    responseBodyItem.setId(userEntity.getId());
    responseBodyItem.setUsername(userEntity.getUsername());
    responseBodyItem.setDisplayName(userEntity.getDisplayName());
    responseBodyItem.setCreatedAt(userEntity.getCreatedAt());
    responseBodyItem.setRoles(roles);

    return responseBodyItem;
  }

  private List<GetUsersResponseBody> mapEntityListToResponseWithRoles(List<UserEntity> userEntityList) {
    List<String> usernames = userEntityList.stream()
        .map(UserEntity::getUsername)
        .toList();

    List<UserRoleEntity> userRoles = userRoleRepository.findByUsernameIn(usernames);

    // 例如: { "用户 1": ["角色 A", "角色 B"]， "用户 2": ["角色 B"] }
    Map<String, List<RoleNameEnums>> rolesByUser = userRoles.stream()
        .collect(Collectors.groupingBy(
            UserRoleEntity::getUsername,
            Collectors.mapping(UserRoleEntity::getRoleName, Collectors.toList())));

    return userEntityList.stream()
        .map(user -> {
          List<RoleNameEnums> roles = rolesByUser.getOrDefault(user.getUsername(), List.of());
          return mapEntityToResponse(user, roles);
        })
        .toList();
  }

  public List<GetUsersResponseBody> getUsers(GetUsersRequestBody requestBody) {
    List<UserEntity> userEntityList = getUsersMapper.query(requestBody);

    return mapEntityListToResponseWithRoles(userEntityList);
  }
}
