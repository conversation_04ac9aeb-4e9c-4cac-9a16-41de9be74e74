package com.baosight.api.feature.admin.route.update_user_roles;

import java.util.ArrayList;
import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import com.baosight.api.db.role.RoleNameEnums;
import com.baosight.api.db.user.UserRepository;
import com.baosight.api.db.user_role.UserRoleEntity;
import com.baosight.api.db.user_role.UserRoleRepository;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class UpdateUserRolesService {
  private final UserRepository userRepository;

  private final UserRoleRepository userRoleRepository;

  private void checkUserRolesCanBeUpdated(UpdateUserRolesRequestBody requestBody) {
    if ("root".equals(requestBody.getUsername())) {
      throw new ResponseStatusException(HttpStatus.UNPROCESSABLE_ENTITY, "禁止修改 root 用户角色");
    }

    userRepository.findByUsername(requestBody.getUsername())
        .orElseThrow(
            () -> new ResponseStatusException(HttpStatus.UNPROCESSABLE_ENTITY, "用户不存在"));

    for (String roleName : requestBody.getRoles()) {
      try {
        RoleNameEnums.valueOf(roleName);
      } catch (IllegalArgumentException e) {
        throw new ResponseStatusException(HttpStatus.UNPROCESSABLE_ENTITY, roleName + "角色不存在");
      }
    }
  }

  @Transactional
  public void updateUserRoles(UpdateUserRolesRequestBody requestBody) {
    checkUserRolesCanBeUpdated(requestBody);

    userRoleRepository.deleteByUsername(requestBody.getUsername());

    List<UserRoleEntity> userRoleEntityList = new ArrayList<>();
    for (String roleName : requestBody.getRoles()) {
      UserRoleEntity userRoleEntity = new UserRoleEntity();
      userRoleEntity.setUsername(requestBody.getUsername());
      userRoleEntity.setRoleName(RoleNameEnums.valueOf(roleName));
      userRoleEntityList.add(userRoleEntity);
    }

    userRoleRepository.saveAll(userRoleEntityList);
  }
}
