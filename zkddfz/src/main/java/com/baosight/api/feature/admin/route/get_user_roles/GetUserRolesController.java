package com.baosight.api.feature.admin.route.get_user_roles;

import java.util.List;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baosight.api.constants.ApiPaths;
import com.baosight.api.db.user_role.UserRoleEntity;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
public class GetUserRolesController {
  private final GetUserRolesService userRolesService;

  @PostMapping(ApiPaths.Admin.GET_USER_ROLES)
  public List<UserRoleEntity> handleRequest(@Valid @RequestBody GetUserRolesRequestBody requestBody) {
    return userRolesService.getUserRoles(requestBody);
  }
}
