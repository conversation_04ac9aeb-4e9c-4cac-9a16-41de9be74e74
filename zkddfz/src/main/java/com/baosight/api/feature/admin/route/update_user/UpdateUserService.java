package com.baosight.api.feature.admin.route.update_user;

import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import com.baosight.api.db.user.UserEntity;
import com.baosight.api.db.user.UserRepository;
import com.baosight.api.feature.user.security.PasswordService;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class UpdateUserService {
  private final PasswordService passwordService;

  private final UserRepository userRepository;

  public void updateUser(UpdateUserRequestBody requestBody) {
    String username = requestBody.getUsername();

    UserEntity user = userRepository.findByUsername(username)
        .orElseThrow(() -> new ResponseStatusException(HttpStatus.UNPROCESSABLE_ENTITY, "用户不存在"));

    if (requestBody.getDisplayName() != null) {
      user.setDisplayName(requestBody.getDisplayName());
    }

    if (requestBody.getPassword() != null) {
      user.setPasswordHash(passwordService.hash(requestBody.getPassword()));
    }

    userRepository.save(user);
  }
}
