package com.baosight.api.feature.user.route.logout;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baosight.api.constants.ApiPaths;
import com.baosight.api.feature.user.security.AccessTokenService;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
public class LogoutController {
  private final AccessTokenService accessTokenService;

  @PostMapping(ApiPaths.User.LOGOUT)
  public void handleRequest(HttpServletResponse response) {
    Cookie cookie = accessTokenService.createLogoutCookie();
    response.addCookie(cookie);
  }
}
