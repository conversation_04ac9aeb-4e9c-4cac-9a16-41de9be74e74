import { emitter } from '@/emitter';
import { useQueryClient } from '@tanstack/vue-query';
import type { GetContextMenuItemsParams, MenuItemDef } from 'ag-grid-community';
import { Modal, message } from 'ant-design-vue';
import { tableId, upsertDrawerId } from './constants';
import { deleteUserByUsername } from './services';

export const useGetContextMenuItems = () => {
  const queryClient = useQueryClient();

  return (params: GetContextMenuItemsParams): MenuItemDef[] => {
    if (!params.node?.data) {
      return [];
    }

    const editAction = () => {
      emitter.emit('app:drawer', { id: upsertDrawerId, data: params.node?.data.username });
    };

    const deleteAction = () => {
      Modal.confirm({
        title: '确认删除',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          await deleteUserByUsername(params.node?.data.username);
          await queryClient.invalidateQueries({ queryKey: [tableId] });
          message.success('删除成功');
        },
      });
    };

    return [
      { name: '修改', action: editAction },
      { name: '删除', action: deleteAction },
    ];
  };
};
