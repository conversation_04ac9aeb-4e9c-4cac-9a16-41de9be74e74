<template>
  <div :class="background">
    <div class="flex justify-center pt-10 px-4">
      <form class="w-full max-w-80 space-y-4" @submit.prevent="handleSubmit">
        <h1 class="text-2xl text-center font-bold">账号登录</h1>

        <div class="space-y-2">
          <label for="username">用户名</label>
          <a-input v-model:value="username" autofocus />
        </div>

        <div class="space-y-2">
          <label for="password">密码</label>
          <a-input v-model:value="password" type="password" />
        </div>

        <div>
          <a-button block type="primary" html-type="submit" :loading="isPending">登录</a-button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { api } from '@/common/api';
import { ApiPaths } from '@/constants/api-paths';
import { useDark } from '@vueuse/core';
import { computed, ref } from 'vue';

const isDark = useDark();
const background = computed(() => (isDark.value ? 'bg-dark' : 'bg-light'));

const username = ref();
const password = ref('');

const isPending = ref(false);

const handleSubmit = async () => {
  isPending.value = true;

  try {
    await api.post(ApiPaths.User.LOGIN, { username: username.value, password: password.value });
    location.assign('/');
  } finally {
    isPending.value = false;
  }
};
</script>

<style scoped>
.bg-dark {
  width: 100vw;
  height: 100vh;
  background-image: url('./assets/bg-dark.jpg');
  background-repeat: no-repeat;
  background-size: cover;
}

.bg-light {
  width: 100vw;
  height: 100vh;
  background-image: url('./assets/bg-light.svg');
  background-repeat: no-repeat;
  background-size: cover;
}
</style>
