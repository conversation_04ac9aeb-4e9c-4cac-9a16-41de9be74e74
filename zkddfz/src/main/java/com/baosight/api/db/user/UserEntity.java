package com.baosight.api.db.user;

import java.time.Instant;

import org.hibernate.annotations.Comment;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Data
@Entity
@Table(name = "bx_user")
@Comment("用户表")
public class UserEntity {
  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Comment("ID")
  private Long id;

  @Column(name = "username", nullable = false, unique = true)
  @Comment("用户名")
  private String username;

  @Column(name = "display_name", nullable = false)
  @Comment("姓名")
  private String displayName;

  @Column(name = "password_hash", nullable = false)
  @Comment("密码哈希")
  private String passwordHash;

  @CreationTimestamp
  @Column(name = "created_at", nullable = false, updatable = false)
  @Comment("创建时间")
  private Instant createdAt;

  @UpdateTimestamp
  @Column(name = "updated_at", nullable = false)
  @Comment("更新时间")
  private Instant updatedAt;
}
