package com.baosight.api.feature.user.route.session;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baosight.api.db.role.RoleNameEnums;
import com.baosight.api.db.user.UserEntity;
import com.baosight.api.db.user_role.UserRoleEntity;
import com.baosight.api.db.user_role.UserRoleRepository;
import com.baosight.api.feature.user.security.UserContextService;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class SessionService {
  private final UserContextService userContextService;

  private final UserRoleRepository userRoleRepository;

  private List<RoleNameEnums> getUserRoles(String username) {
    return userRoleRepository.findAllByUsername(username).stream().map(UserRoleEntity::getRoleName).toList();
  }

  public SessionResponseBody getSessionResponseBody(HttpServletRequest request) {
    UserEntity user = userContextService.getUserFromRequest(request);

    List<RoleNameEnums> roles = getUserRoles(user.getUsername());

    SessionResponseBody responseBody = new SessionResponseBody();
    responseBody.setUsername(user.getUsername());
    responseBody.setDisplayName(user.getDisplayName());
    responseBody.setRoles(roles);

    return responseBody;
  }
}
