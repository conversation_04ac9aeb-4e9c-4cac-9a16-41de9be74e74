const fs = require('fs');
const path = require('path');
const os = require('os');

// 获取项目名（当前目录名）
function getProjectName() {
  const currentDir = process.cwd();
  const projectName = path.basename(currentDir);

  console.log(`📁 当前目录: ${currentDir}`);
  console.log(`📋 项目名称: ${projectName}`);

  // 检查是否与框架名称相同
  if (projectName.toLowerCase() === 'equip4j') {
    console.warn('⚠️  警告：当前项目名称与框架名称相同 (equip4j)');
    console.warn('   建议重命名项目目录以避免混淆');
    console.warn('   例如: my-project, demo-app, test-project 等');
    console.warn('');
  }

  return projectName;
}

// 运行时工具配置
const RUNTIME_TOOLS = {
  nodejs: {
    keywords: ['node', 'nodejs'],
    required: true,
    settingKey: 'node.home',
  },
  maven: {
    keywords: ['apache-maven', 'maven'],
    required: true,
    settingKey: 'maven.home',
  },
  jdk: {
    keywords: ['jdk', 'java', 'openjdk'],
    required: true,
    settingKey: 'java.jdt.ls.java.home',
  },
};

/**
 * 读取 runtime 文件夹并匹配运行时工具
 */
function detectRuntimeTools() {
  const runtimeDir = path.join(process.cwd(), 'runtime');

  // 检查 runtime 文件夹是否存在
  if (!fs.existsSync(runtimeDir)) {
    console.error('错误：找不到 runtime 文件夹');
    console.error('   请确保在项目根目录下运行此脚本');
    process.exit(1);
  }

  // 读取文件夹列表
  const folders = fs
    .readdirSync(runtimeDir, { withFileTypes: true })
    .filter(dirent => dirent.isDirectory())
    .map(dirent => dirent.name);

  if (folders.length === 0) {
    console.error('错误：runtime 文件夹为空');
    console.error('   请确保已下载必要的运行时工具');
    process.exit(1);
  }

  console.log(`🔍 检测到 runtime 文件夹: ${folders.join(', ')}`);

  const detectedTools = {};

  // 匹配每种运行时工具
  for (const [toolName, config] of Object.entries(RUNTIME_TOOLS)) {
    const matchedFolders = folders.filter(folder =>
      config.keywords.some(keyword => folder.toLowerCase().includes(keyword.toLowerCase())),
    );

    if (matchedFolders.length === 0) {
      if (config.required) {
        console.error(`错误：未找到 ${toolName} 运行时`);
        console.error(`   期望包含关键词: ${config.keywords.join(', ')}`);
        console.error(`   当前文件夹: ${folders.join(', ')}`);
        process.exit(1);
      }
    } else if (matchedFolders.length > 1) {
      const selectedFolder = matchedFolders[matchedFolders.length - 1];
      console.warn(`⚠️  警告：发现多个 ${toolName} 版本: ${matchedFolders.join(', ')}`);
      console.warn(`   将使用最后一个: ${selectedFolder}`);
      detectedTools[toolName] = selectedFolder;
    } else {
      detectedTools[toolName] = matchedFolders[0];
      console.log(`✅ 检测到 ${toolName}: ${matchedFolders[0]}`);
    }
  }

  return detectedTools;
}

/**
 * 生成工具路径
 */
function generateToolPaths(detectedTools) {
  const workspaceRoot = process.cwd();
  const paths = {};

  for (const [toolName, folderName] of Object.entries(detectedTools)) {
    const config = RUNTIME_TOOLS[toolName];
    if (!config.settingKey) continue;

    let toolPath = path.join(workspaceRoot, 'runtime', folderName);

    // macOS 系统的 JDK 需要添加 /Contents/Home
    if (toolName === 'jdk' && os.platform() === 'darwin') {
      const contentsHomePath = path.join(toolPath, 'Contents', 'Home');
      if (fs.existsSync(contentsHomePath)) {
        toolPath = contentsHomePath;
        console.log(`🍎 macOS 系统，JDK 路径调整为: ${path.basename(toolPath)}`);
      }
    }

    paths[config.settingKey] = toolPath;
  }

  return paths;
}

/**
 * 生成 workspace 配置
 */
function generateWorkspaceConfig(toolPaths) {
  const workspaceConfig = {
    settings: {
      ...toolPaths,
      'java.configuration.maven.userSettings': '.mvn/settings.xml',
      'java.compile.nullAnalysis.mode': 'automatic',
      'files.associations': {
        '*.css': 'tailwindcss',
      },
      'git.confirmSync': false,
      'spring-boot.ls.problem.version-validation.UPDATE_LATEST_MAJOR_VERSION': 'IGNORE',
      'spring-boot.ls.problem.version-validation.UPDATE_LATEST_PATCH_VERSION': 'IGNORE',
      'spring-boot.ls.problem.version-validation.UPDATE_LATEST_MINOR_VERSION': 'IGNORE',
      'spring-boot.ls.problem.application-properties.PROP_UNKNOWN_PROPERTY': 'IGNORE',
    },
    folders: [
      {
        path: '.',
      },
    ],
  };

  return workspaceConfig;
}

// 主程序执行
try {
  console.log('🚀 开始生成 VS Code 工作区配置...');
  console.log('');

  // 获取项目名
  const projectName = getProjectName();
  console.log('');

  // 检测运行时工具
  const detectedTools = detectRuntimeTools();
  console.log('');

  // 生成工具路径
  const toolPaths = generateToolPaths(detectedTools);

  // 生成 workspace 配置
  const workspaceConfig = generateWorkspaceConfig(toolPaths);

  // 写入文件
  const workspaceFile = path.join(process.cwd(), `${projectName}.code-workspace`);
  fs.writeFileSync(workspaceFile, JSON.stringify(workspaceConfig, null, 2));

  console.log(`✅ 成功生成工作区配置: ${projectName}.code-workspace`);
  console.log('📋 配置内容:');
  Object.entries(toolPaths).forEach(([key, value]) => {
    // 只显示相对路径部分，让输出更简洁
    const relativePath = path.relative(process.cwd(), value);
    console.log(`   ${key}: ${relativePath}`);
  });
  console.log('');
  console.log('💡 打开方法:');
  console.log(`       - 通过 VS Code 打开项目根目录下的 ${projectName}.code-workspace`);
  console.log('');
  console.log('💡 提示: ');
  console.log('       - 如果 VS Code 提示是否信任工作区，请在点击信任后，关闭 VS Code 并重新打开工作区');
} catch (error) {
  console.error('生成失败:', error.message);
  process.exit(1);
}
