<template>
  <a-drawer
    :keyboard="false"
    :mask-closable="false"
    :mask="false"
    :title="title"
    @after-open-change="afterOpenChange"
    placement="right"
    root-class-name="text-neutral-950 dark:text-neutral-50"
    v-model:open="open"
  >
    <slot></slot>
  </a-drawer>
</template>

<script setup lang="ts">
import { emitter, useEmitter } from '@/emitter';
import { nextTick, ref } from 'vue';

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
});

const emits = defineEmits(['open', 'close']);

const open = ref(false);

const afterOpenChange = (open: boolean) => {
  if (!open) {
    emits('close');
    emitter.emit('app:drawer', { close: true });
  }
};

useEmitter('app:drawer', payload => {
  if (payload.close) {
    open.value = false;
    return;
  }
  if (props.id === payload.id) {
    open.value = true;
    nextTick(() => {
      emits('open', payload.data);
    });
  } else {
    open.value = false;
  }
});
</script>
