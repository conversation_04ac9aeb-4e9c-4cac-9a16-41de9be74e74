<script setup lang="ts">
import { computed } from 'vue';

interface Model {
  id: string;
  name: string;
  currentPosition: { x: number; z: number };
  currentRotation: { y: number };
}

interface Props {
  model: Model;
  loadingStatus: Record<string, 'loaded' | 'loading' | 'error'>;
  firstPersonView: { targetModelId: string | null };
}

const props = defineProps<Props>();
const emit = defineEmits(['toggleFirstPersonView', 'removeModel']);

const status = computed(() => props.loadingStatus[props.model.id] || 'loading');

const statusBadgeClasses = computed(() => ({
  'bg-green-900/20 border border-green-500/50 text-green-400 shadow-md shadow-green-500/20': status.value === 'loaded',
  'bg-red-900/20 border border-red-500/50 text-red-400 shadow-md shadow-red-500/20': status.value === 'error',
  'bg-yellow-900/20 border border-yellow-500/50 text-yellow-400 shadow-md shadow-yellow-500/20':
    status.value === 'loading',
}));

const statusDotClasses = computed(() => ({
  'bg-green-500 animate-pulse': status.value === 'loaded',
  'bg-red-500': status.value === 'error',
  'bg-yellow-500 animate-spin': status.value === 'loading',
}));

const toggleButtonClasses = computed(() => [
  'tech-btn',
  'px-4 py-1.5 rounded-md text-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-[#1a1a1a]',
  props.firstPersonView.targetModelId === props.model.id
    ? 'bg-green-800/30 hover:bg-green-700/40 text-green-300 border border-green-500/50 focus:ring-green-500 shadow-md shadow-green-500/20' // Active state green
    : 'bg-cyan-800/30 hover:bg-cyan-700/40 text-cyan-300 border border-cyan-500/50 focus:ring-cyan-500 shadow-md shadow-cyan-500/20', // Default state cyan
  { 'opacity-50 cursor-not-allowed': status.value !== 'loaded' },
]);

const getStatusText = computed(() => {
  if (status.value === 'loaded') return '在线';
  if (status.value === 'error') return '错误';
  return '加载中';
});
</script>

<template>
  <div
    class="flex flex-col p-6 rounded-lg border border-blue-500/50 shadow-lg shadow-blue-500/20 bg-blue-950/30 backdrop-blur-sm text-white min-w-[300px] max-w-sm"
  >
    <div class="entry-header flex justify-between items-center mb-4">
      <div class="model-id text-cyan-400 text-lg font-bold">{{ model.id.toUpperCase() }}</div>
      <div
        class="model-status-badge flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium"
        :class="statusBadgeClasses"
      >
        <div class="status-dot w-2 h-2 rounded-full" :class="statusDotClasses"></div>
        <span>{{ getStatusText }}</span>
      </div>
    </div>

    <div class="entry-body flex-grow mb-6">
      <div class="model-name text-white text-xl font-bold mb-3">{{ model.name }}</div>
      <div class="model-meta flex flex-wrap gap-3">
        <span
          class="meta-item bg-gray-800/30 border border-gray-700/50 px-3 py-1 rounded-md text-sm text-gray-300 font-mono shadow-sm shadow-gray-700/10"
        >
          POS: {{ model.currentPosition.x.toFixed(1) }}, {{ model.currentPosition.z.toFixed(1) }}
        </span>
        <span
          class="meta-item bg-gray-800/30 border border-gray-700/50 px-3 py-1 rounded-md text-sm text-gray-300 font-mono shadow-sm shadow-gray-700/10"
        >
          ROT: {{ model.currentRotation.y.toFixed(0) }}°
        </span>
        <span
          v-if="firstPersonView.targetModelId === model.id"
          class="meta-item active bg-cyan-800/30 border border-cyan-500/50 px-3 py-1 rounded-md text-sm text-cyan-300 font-medium shadow-md shadow-cyan-500/20"
        >
          👁️ FPV
        </span>
      </div>
    </div>

    <div class="entry-controls flex gap-3 mt-auto">
      <button
        @click="emit('toggleFirstPersonView', model.id)"
        :class="toggleButtonClasses"
        :disabled="status !== 'loaded'"
      >
        {{ firstPersonView.targetModelId === model.id ? '退出视角' : '进入视角' }}
      </button>
      <button
        @click="emit('removeModel', model.id)"
        class="tech-btn btn-danger bg-red-800/30 border border-red-500/50 text-red-400 hover:bg-red-700/40 px-4 py-1.5 rounded-md text-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-[#1a1a1a] focus:ring-red-500 shadow-md shadow-red-500/20"
      >
        移除
      </button>
    </div>
  </div>
</template>
