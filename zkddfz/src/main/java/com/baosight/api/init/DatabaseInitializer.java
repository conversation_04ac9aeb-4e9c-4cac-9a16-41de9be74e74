package com.baosight.api.init;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import com.baosight.api.db.activity.ActivityInitializer;
import com.baosight.api.db.role.RoleInitializer;
import com.baosight.api.db.user.UserInitializer;
import com.baosight.api.db.user_role.UserRoleInitializer;
import com.baosight.api.util.SpringProfileChecker;

import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class DatabaseInitializer {
  private static final Logger logger = LoggerFactory.getLogger(DatabaseInitializer.class);

  private final ActivityInitializer activityInitializer;

  private final UserInitializer userInitializer;

  private final RoleInitializer roleInitializer;

  private final UserRoleInitializer userRoleInitializer;

  private final SpringProfileChecker springProfileChecker;

  @EventListener(ApplicationReadyEvent.class)
  public void init() {
    if (springProfileChecker.isDevelopmentProfile() || springProfileChecker.isTestingProfile()) {
      logger.info("应用启动完成，开始初始化数据库数据...");

      activityInitializer.init();

      roleInitializer.init();

      userInitializer.init();

      userRoleInitializer.init();

      logger.info("数据库数据初始化完成！");
    } else {
      logger.info("非开发环境，跳过数据初始化");
    }
  }
}
