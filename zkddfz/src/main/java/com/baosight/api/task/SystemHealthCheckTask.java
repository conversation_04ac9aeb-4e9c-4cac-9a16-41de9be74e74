package com.baosight.api.task;

import java.sql.Connection;

import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;

/**
 * 系统健康检查任务
 * 定期检查系统关键组件状态
 */
@Component
@RequiredArgsConstructor
public class SystemHealthCheckTask {
  private static final Logger logger = LoggerFactory.getLogger(SystemHealthCheckTask.class);

  private static final int DATABASE_CHECK_INTERVAL_MILLISECONDS = 5 * 60 * 1000; // 5 分钟
  private static final int MEMORY_CHECK_INTERVAL_MILLISECONDS = 10 * 60 * 1000; // 10 分钟
  private static final int CONNECTION_TIMEOUT_SECONDS = 5;
  private static final double HIGH_MEMORY_THRESHOLD = 85.0;
  private static final long BYTES_TO_MB = (long) 1024 * 1024;

  private final DataSource dataSource;

  /**
   * 数据库连接健康检查
   * 每 5 分钟检查一次
   */
  @Scheduled(fixedRate = DATABASE_CHECK_INTERVAL_MILLISECONDS)
  public void checkDatabaseHealth() {
    try (Connection connection = dataSource.getConnection()) {
      boolean isValid = connection.isValid(CONNECTION_TIMEOUT_SECONDS); // 5 秒超时

      if (isValid) {
        logger.info("周期任务: 数据库连接健康检查通过");
      } else {
        logger.warn("周期任务: 数据库连接异常！");
      }
    } catch (Exception e) {
      logger.error("周期任务: 数据库健康检查失败", e);
    }
  }

  /**
   * 内存使用情况检查
   * 每 10 分钟检查一次
   */
  @Scheduled(fixedRate = MEMORY_CHECK_INTERVAL_MILLISECONDS)
  public void checkMemoryUsage() {
    Runtime runtime = Runtime.getRuntime();

    long maxMemory = runtime.maxMemory();
    long totalMemory = runtime.totalMemory();
    long freeMemory = runtime.freeMemory();
    long usedMemory = totalMemory - freeMemory;

    double usagePercentage = (double) usedMemory / maxMemory * 100;
    String usagePercentageString = String.format("%.2f", usagePercentage);

    logger.info("周期任务: JVM 堆内存 - 已用: {}MB, 已分配: {}MB, 最大: {}MB, 使用率: {}%",
        usedMemory / BYTES_TO_MB,
        totalMemory / BYTES_TO_MB,
        maxMemory / BYTES_TO_MB,
        usagePercentageString);

    if (usagePercentage > HIGH_MEMORY_THRESHOLD) {
      logger.warn("周期任务: JVM 堆内存使用率过高: {}%", usagePercentageString);
    }
  }
}
