package com.baosight.api.db.activity;

import org.springframework.stereotype.Service;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ActivityInitializer {
  private static final String root = "root";

  private final ActivityRepository activityRepository;

  private void upsert(String username, String activity) {
    ActivityEntity activityEntity = activityRepository.findByUsernameAndActivity(username, activity)
        .orElse(new ActivityEntity());

    activityEntity.setActivity(activity);
    activityEntity.setUsername(username);

    activityRepository.save(activityEntity);
  }

  @Transactional
  public void init() {
    upsert(root, "创建用户表");
    upsert(root, "创建角色表");
    upsert(root, "创建用户-角色表");
    upsert(root, "创建活动表");
  }
}
