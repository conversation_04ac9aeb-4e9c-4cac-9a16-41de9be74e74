package com.baosight.api.db.user_role;

import org.springframework.stereotype.Service;

import com.baosight.api.db.role.RoleNameEnums;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class UserRoleInitializer {
  private final UserRoleRepository userRoleRepository;

  private void upsert(String username, RoleNameEnums roleName) {
    UserRoleEntity userRoleEntity = userRoleRepository.findByUsernameAndRoleName(username, roleName)
        .orElse(new UserRoleEntity());

    userRoleEntity.setUsername(username);
    userRoleEntity.setRoleName(roleName);

    userRoleRepository.save(userRoleEntity);
  }

  @Transactional
  public void init() {
    upsert("root", RoleNameEnums.管理员);
    upsert("user", RoleNameEnums.用户);
    upsert("deleted", RoleNameEnums.用户);
    upsert("suspended", RoleNameEnums.管理员);
  }
}
