package com.baosight.api.exception;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.servlet.resource.NoResourceFoundException;

/**
 * 全局异常处理器
 * 统一处理应用中的各种异常，返回标准化的错误响应
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
  private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

  private static final String MESSAGE_KEY = "message";
  private static final String FIELD_KEY = "field";
  private static final String INVALID_JSON_MESSAGE = "请求体必须是合法 JSON";
  private static final String NOT_FOUND_MESSAGE = "路由不存在";

  /**
   * 处理参数校验异常
   */
  @ExceptionHandler(MethodArgumentNotValidException.class)
  public ResponseEntity<Map<String, String>> handleValidationException(MethodArgumentNotValidException e) {
    String field = e.getBindingResult().getFieldErrors().get(0).getField();
    String message = e.getBindingResult().getFieldErrors().get(0).getDefaultMessage();

    return new ResponseEntity<>(Map.of(FIELD_KEY, field, MESSAGE_KEY, message), HttpStatus.UNPROCESSABLE_ENTITY);
  }

  /**
   * 处理请求体不可读异常（如 JSON 格式错误）
   */
  @ExceptionHandler(HttpMessageNotReadableException.class)
  public ResponseEntity<Map<String, String>> handleMessageNotReadableException(HttpMessageNotReadableException e) {
    return new ResponseEntity<>(Map.of(MESSAGE_KEY, INVALID_JSON_MESSAGE), HttpStatus.UNPROCESSABLE_ENTITY);
  }

  /**
   * 处理响应状态异常
   */
  @ExceptionHandler(ResponseStatusException.class)
  public ResponseEntity<Map<String, String>> handleResponseStatusException(ResponseStatusException e) {
    String reason = e.getReason();
    HttpStatusCode statusCode = e.getStatusCode();

    if (reason == null || reason.isEmpty()) {
      return new ResponseEntity<>(statusCode);
    }

    return new ResponseEntity<>(Map.of(MESSAGE_KEY, reason), statusCode);
  }

  /**
   * 处理资源未找到异常
   */
  @ExceptionHandler(NoResourceFoundException.class)
  public ResponseEntity<Map<String, String>> handleResourceNotFoundException(NoResourceFoundException e) {
    return new ResponseEntity<>(Map.of(MESSAGE_KEY, NOT_FOUND_MESSAGE), HttpStatus.NOT_FOUND);
  }

  /**
   * 处理其他未捕获的异常
   */
  @ExceptionHandler(Exception.class)
  public ResponseEntity<Map<String, String>> handleGenericException(Exception e) {
    logger.error("", e);
    return new ResponseEntity<>(Map.of(MESSAGE_KEY, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
  }
}
