package com.baosight.api.feature.admin.route.add_user;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baosight.api.constants.ApiPaths;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
public class AddUserController {
  private final AddUserService userService;

  @PostMapping(ApiPaths.Admin.ADD_USER)
  public void handleRequest(@Valid @RequestBody AddUserRequestBody requestBody, HttpServletRequest request) {
    userService.addUser(requestBody);
  }
}
