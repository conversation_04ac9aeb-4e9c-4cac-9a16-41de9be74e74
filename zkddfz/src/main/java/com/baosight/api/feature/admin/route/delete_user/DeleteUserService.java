package com.baosight.api.feature.admin.route.delete_user;

import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import com.baosight.api.db.activity.ActivityRepository;
import com.baosight.api.db.user.UserRepository;
import com.baosight.api.db.user_role.UserRoleRepository;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class DeleteUserService {
  private final UserRepository userRepository;

  private final UserRoleRepository userRoleRepository;

  private final ActivityRepository activityRepository;

  private void checkUserCanBeDeleted(DeleteUserRequestBody requestBody) {
    String username = requestBody.getUsername();

    if ("root".equals(username)) {
      throw new ResponseStatusException(HttpStatus.UNPROCESSABLE_ENTITY, "root 用户不可删除");
    }

    userRepository.findByUsername(username)
        .orElseThrow(
            () -> new ResponseStatusException(HttpStatus.UNPROCESSABLE_ENTITY, "用户不存在"));
  }

  @Transactional
  public void deleteUser(DeleteUserRequestBody requestBody) {
    checkUserCanBeDeleted(requestBody);

    userRepository.deleteByUsername(requestBody.getUsername());
    userRoleRepository.deleteByUsername(requestBody.getUsername());
    activityRepository.deleteByUsername(requestBody.getUsername());
  }
}
