<template />

<script setup lang="ts">
import { api } from '@/common/api';
import { dayjs } from '@/common/dayjs';
import { useQuery } from '@tanstack/vue-query';
import { ref } from 'vue';

const cachedContent = ref('');

useQuery({
  queryKey: ['auto-update'],
  queryFn: async () => {
    const { data } = await api.get(`/?t=${dayjs().unix()}`);
    if (!cachedContent.value) {
      cachedContent.value = data;
      return;
    }
    if (data !== cachedContent.value) {
      location.reload();
    }
  },
  refetchInterval: 60000,
  enabled: !import.meta.env.DEV,
});
</script>
