package com.baosight.api.controller;

import com.baosight.api.websocket.RouteWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * UDP广播管理控制器
 * 提供UDP广播服务的管理接口
 */
@RestController
@RequestMapping("/api/udp-broadcast")
@CrossOrigin(origins = "*")
public class UdpBroadcastController {

    @Autowired
    private RouteWebSocketHandler routeWebSocketHandler;

    /**
     * 获取UDP广播器状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        String status = routeWebSocketHandler.getUdpBroadcasterStatus();
        
        Map<String, Object> response = Map.of(
            "status", "running",
            "broadcastInfo", status,
            "timestamp", System.currentTimeMillis(),
            "message", "UDP广播器状态信息"
        );

        return ResponseEntity.ok(response);
    }

    /**
     * 添加UDP广播订阅者
     */
    @PostMapping("/subscribers")
    public ResponseEntity<Map<String, Object>> addSubscriber(
            @RequestParam String address,
            @RequestParam int port) {
        
        try {
            routeWebSocketHandler.addUdpSubscriber(address, port);
            
            Map<String, Object> response = Map.of(
                "success", true,
                "message", "订阅者添加成功",
                "subscriber", Map.of("address", address, "port", port),
                "timestamp", System.currentTimeMillis()
            );
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = Map.of(
                "success", false,
                "message", "添加订阅者失败: " + e.getMessage(),
                "timestamp", System.currentTimeMillis()
            );
            
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 移除UDP广播订阅者
     */
    @DeleteMapping("/subscribers")
    public ResponseEntity<Map<String, Object>> removeSubscriber(
            @RequestParam String address,
            @RequestParam int port) {
        
        try {
            routeWebSocketHandler.removeUdpSubscriber(address, port);
            
            Map<String, Object> response = Map.of(
                "success", true,
                "message", "订阅者移除成功",
                "subscriber", Map.of("address", address, "port", port),
                "timestamp", System.currentTimeMillis()
            );
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = Map.of(
                "success", false,
                "message", "移除订阅者失败: " + e.getMessage(),
                "timestamp", System.currentTimeMillis()
            );
            
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取UDP广播配置信息
     */
    @GetMapping("/config")
    public ResponseEntity<Map<String, Object>> getConfig() {
        Map<String, Object> config = Map.of(
            "description", "UDP广播配置信息",
            "broadcastPort", 9091,
            "broadcastAddress", "***************",
            "enabled", true,
            "features", Map.of(
                "vehicleDataBroadcast", "实时车辆数据广播",
                "subscriberManagement", "订阅者管理",
                "automaticBroadcast", "接收到WebSocket数据时自动广播"
            ),
            "timestamp", System.currentTimeMillis()
        );

        return ResponseEntity.ok(config);
    }

    /**
     * 获取使用说明
     */
    @GetMapping("/help")
    public ResponseEntity<Map<String, Object>> getHelp() {
        Map<String, Object> help = Map.of(
            "title", "UDP广播服务使用说明",
            "description", "UDP广播服务用于将WebSocket接收到的车辆实时数据通过UDP协议广播给外部系统",
            "endpoints", Map.of(
                "GET /api/udp-broadcast/status", "获取UDP广播器状态",
                "POST /api/udp-broadcast/subscribers", "添加UDP广播订阅者",
                "DELETE /api/udp-broadcast/subscribers", "移除UDP广播订阅者",
                "GET /api/udp-broadcast/config", "获取UDP广播配置信息"
            ),
            "usage", Map.of(
                "automaticBroadcast", "当WebSocket接收到车辆状态数据时，会自动通过UDP广播",
                "defaultBroadcast", "默认广播到 ***************:9091",
                "subscriberBroadcast", "同时广播到所有已添加的订阅者地址"
            ),
            "dataFormat", Map.of(
                "type", "JSON数组",
                "structure", "与WebSocket接收的车辆状态数据格式相同",
                "example", "[{\"device_id\":\"PCC_12345\",\"position\":{\"x\":100,\"y\":0,\"z\":200},\"rotation\":{\"x\":0,\"y\":90,\"z\":0},\"duration\":0,\"power\":85,\"consume\":15,\"type\":\"PCC\"}]"
            ),
            "timestamp", System.currentTimeMillis()
        );

        return ResponseEntity.ok(help);
    }
}
