/**
 * 流畅动画测试工具
 * 用于测试和验证流畅动画控制器的功能
 */

import { SmoothAnimationController } from './SmoothAnimationController.js';

export class SmoothAnimationTest {
  constructor() {
    this.controller = null;
    this.testDevices = ['car1', 'car2', 'car3'];
    this.isRunning = false;
    this.testInterval = null;
    this.dataCounter = 0;
    
    console.log('SmoothAnimationTest 初始化完成');
  }

  /**
   * 初始化测试环境
   */
  initialize() {
    this.controller = new SmoothAnimationController({
      bufferSize: 3,
      predictionTime: 1.5,
      smoothingFactor: 0.8,
      maxInterpolationTime: 2.0,
      adaptiveBuffer: true
    });

    // 设置位置更新回调
    this.controller.setPositionUpdateCallback((data) => {
      this.handlePositionUpdate(data);
    });

    console.log('测试环境初始化完成');
  }

  /**
   * 开始模拟数据测试
   */
  startTest() {
    if (this.isRunning) {
      console.warn('⚠️ 测试已在运行中');
      return;
    }

    if (!this.controller) {
      this.initialize();
    }

    this.isRunning = true;
    this.dataCounter = 0;

    // 模拟每秒接收一次数据
    this.testInterval = setInterval(() => {
      this.generateTestData();
    }, 1000);

    console.log('🚀 开始流畅动画测试 - 每秒生成一次数据');
  }

  /**
   * 停止测试
   */
  stopTest() {
    if (!this.isRunning) {
      console.warn('⚠️ 测试未在运行');
      return;
    }

    this.isRunning = false;

    if (this.testInterval) {
      clearInterval(this.testInterval);
      this.testInterval = null;
    }

    if (this.controller) {
      this.controller.stopAllAnimations();
    }

    console.log('🛑 流畅动画测试已停止');
  }

  /**
   * 生成测试数据
   */
  generateTestData() {
    this.dataCounter++;
    const currentTime = Date.now();

    this.testDevices.forEach((deviceId, index) => {
      // 生成模拟的车辆移动数据
      const dataPoint = this.generateVehicleData(deviceId, index, this.dataCounter);
      
      // 添加到流畅动画控制器
      this.controller.addDataPoint(deviceId, dataPoint);
      
      console.log(`📊 生成设备 ${deviceId} 的测试数据:`, dataPoint);
    });
  }

  /**
   * 生成车辆数据
   */
  generateVehicleData(deviceId, deviceIndex, counter) {
    const time = counter * 1000; // 每秒一个数据点
    const baseX = deviceIndex * 100; // 不同车辆的基础X位置
    const baseZ = deviceIndex * 50;  // 不同车辆的基础Z位置

    // 生成圆形轨迹
    const radius = 50;
    const angle = (counter * 0.2) + (deviceIndex * Math.PI * 2 / 3); // 每个车辆相位不同

    const position = {
      x: baseX + Math.cos(angle) * radius,
      y: 0,
      z: baseZ + Math.sin(angle) * radius
    };

    const rotation = {
      x: 0,
      y: (angle * 180 / Math.PI) % 360, // 朝向运动方向
      z: 0
    };

    // 模拟电量逐渐减少
    const power = Math.max(0, 100 - counter * 2);
    
    // 模拟累计消耗增加
    const consume = counter * 1.5;

    return {
      position,
      rotation,
      power,
      consume,
      distance: counter * 10,
      duration: 1000, // 1秒到达下一个点
      timestamp: Date.now()
    };
  }

  /**
   * 处理位置更新
   */
  handlePositionUpdate(data) {
    const { deviceId, position, rotation, velocity, smooth, timestamp } = data;
    
    // 在控制台输出位置更新信息（限制频率）
    if (Math.random() < 0.1) { // 10%的概率输出，避免日志过多
      console.log(`设备 ${deviceId} 位置更新:`, {
        position: {
          x: position.x.toFixed(2),
          y: position.y.toFixed(2),
          z: position.z.toFixed(2)
        },
        rotation: {
          y: rotation.y.toFixed(1)
        },
        velocity: {
          x: velocity.x.toFixed(2),
          z: velocity.z.toFixed(2)
        },
        smooth
      });
    }

    // 这里可以添加实际的3D模型位置更新逻辑
    this.updateVirtualModel(deviceId, position, rotation);
  }

  /**
   * 更新虚拟模型（模拟3D场景更新）
   */
  updateVirtualModel(deviceId, position, rotation) {
    // 模拟更新3D模型位置
    // 在实际应用中，这里会调用Three.js或其他3D引擎的API
    
    // 可以在这里添加性能监控
    this.trackPerformance(deviceId);
  }

  /**
   * 性能监控
   */
  trackPerformance(deviceId) {
    if (this.controller) {
      const status = this.controller.getDeviceStatus(deviceId);
      
      // 每10秒输出一次性能统计
      if (this.dataCounter % 10 === 0) {
        const stats = this.controller.getPerformanceStats();
        console.log(`📈 性能统计 (${deviceId}):`, {
          bufferSize: status.bufferSize,
          isAnimating: status.isAnimating,
          networkDelay: status.networkDelay.toFixed(2) + 'ms',
          performanceStats: stats
        });
      }
    }
  }

  /**
   * 测试不同配置
   */
  testDifferentConfigurations() {
    console.log('测试不同配置...');

    const configs = [
      { bufferSize: 1, predictionTime: 0.5, smoothingFactor: 0.5 },
      { bufferSize: 3, predictionTime: 1.5, smoothingFactor: 0.8 },
      { bufferSize: 5, predictionTime: 2.0, smoothingFactor: 0.9 }
    ];

    configs.forEach((config, index) => {
      setTimeout(() => {
        console.log(`应用配置 ${index + 1}:`, config);

        if (this.controller) {
          this.controller.options = { ...this.controller.options, ...config };
          console.log('配置已更新');
        }
      }, index * 5000); // 每5秒切换一次配置
    });
  }

  /**
   * 模拟网络延迟和丢包
   */
  simulateNetworkIssues() {
    console.log('🌐 模拟网络问题...');

    // 模拟随机延迟
    const originalGenerateTestData = this.generateTestData.bind(this);
    this.generateTestData = () => {
      const delay = Math.random() * 500; // 0-500ms随机延迟
      
      setTimeout(() => {
        // 模拟10%的丢包率
        if (Math.random() > 0.1) {
          originalGenerateTestData();
        } else {
          console.log('📦 模拟数据包丢失');
        }
      }, delay);
    };

    console.log('✅ 网络问题模拟已启用');
  }

  /**
   * 获取测试报告
   */
  getTestReport() {
    if (!this.controller) {
      return { error: '测试未初始化' };
    }

    const report = {
      isRunning: this.isRunning,
      dataCounter: this.dataCounter,
      performanceStats: this.controller.getPerformanceStats(),
      deviceStatuses: this.testDevices.map(deviceId => ({
        deviceId,
        status: this.controller.getDeviceStatus(deviceId)
      }))
    };

    console.log('📊 测试报告:', report);
    return report;
  }

  /**
   * 清理测试环境
   */
  cleanup() {
    this.stopTest();
    
    if (this.controller) {
      this.controller.clearAll();
      this.controller = null;
    }

    console.log('🧹 测试环境已清理');
  }
}

// 创建全局测试实例
window.SmoothAnimationTest = SmoothAnimationTest;

export default SmoothAnimationTest;
